import CrudModule from '@/modules/CrudModule/CrudModule';
import AdminForm from '@/forms/AdminForm';

import useLanguage from '@/locale/useLanguage';

export default function Admin() {
  const translate = useLanguage();
  const entity = 'admin';
  const searchConfig = {
    displayLabels: ['name', 'email'],
    searchFields: 'name,email',
  };
  const deleteModalLabels = ['name', 'email'];

  const Labels = {
    PANEL_TITLE: translate('admin'),
    DATATABLE_TITLE: translate('admin_list'),
    ADD_NEW_ENTITY: translate('add_new_admin'),
    ENTITY_NAME: translate('admin'),
  };
  const configPage = {
    entity,
    ...Labels,
  };
  const config = {
    ...configPage,
    searchConfig,
    deleteModalLabels,
  };
  return (
    <CrudModule
      createForm={<AdminForm isUpdateForm={false} />}
      updateForm={<AdminForm isUpdateForm={true} />}
      config={config}
    />
  );
}
