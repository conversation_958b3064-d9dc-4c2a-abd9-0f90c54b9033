import React, { useState, useEffect } from 'react';
import { Card, Tag, Avatar, Tooltip, Button, Modal, message } from 'antd';
import { UserOutlined, CalendarOutlined, ToolOutlined, DollarOutlined } from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

import { request } from '@/request';
import useLanguage from '@/locale/useLanguage';

const stages = [
  { id: 'BACKLOG', title: 'Backlog', color: '#f0f0f0', icon: '📋' },
  { id: 'SCHEDULED', title: 'Zaplanowane', color: '#e6f7ff', icon: '📅' },
  { id: 'IN_PROGRESS', title: 'W trakcie', color: '#fff2e6', icon: '🔧' },
  { id: 'PENDING_PARTS', title: 'Oczekiwanie na cz<PERSON>', color: '#fff1f0', icon: '📦' },
  { id: 'QUALITY_CHECK', title: '<PERSON><PERSON><PERSON><PERSON> jak<PERSON>', color: '#f6ffed', icon: '✅' },
  { id: 'COMPLETED', title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', color: '#f0f9ff', icon: '🎉' },
  { id: 'BILLED', title: 'Rozliczone', color: '#f9f0ff', icon: '💰' },
  { id: 'CLOSED', title: 'Archiwum', color: '#f5f5f5', icon: '📁' },
];

const priorityColors = {
  low: '#52c41a',
  medium: '#faad14',
  high: '#ff4d4f',
  urgent: '#722ed1',
};

const typeIcons = {
  maintenance: '🔧',
  repair: '⚡',
  installation: '🏗️',
  inspection: '🔍',
  emergency: '🚨',
};

export default function KanbanBoard() {
  const translate = useLanguage();
  const [kanbanData, setKanbanData] = useState({});
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    fetchKanbanData();
  }, []);

  const fetchKanbanData = async () => {
    try {
      setLoading(true);
      const response = await request.get({ entity: 'serviceorder/kanban' });
      if (response.success) {
        setKanbanData(response.result);
      }
    } catch (error) {
      message.error('Błąd podczas ładowania danych Kanban');
    } finally {
      setLoading(false);
    }
  };

  const handleDragEnd = async (result) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) return;

    try {
      // Update stage in backend
      await request.patch({
        entity: `serviceorder/stage/${draggableId}`,
        jsonData: { stage: destination.droppableId }
      });

      // Update local state
      const newKanbanData = { ...kanbanData };
      const sourceOrders = [...newKanbanData[source.droppableId]];
      const destOrders = source.droppableId === destination.droppableId 
        ? sourceOrders 
        : [...newKanbanData[destination.droppableId]];

      const [movedOrder] = sourceOrders.splice(source.index, 1);
      movedOrder.stage = destination.droppableId;
      destOrders.splice(destination.index, 0, movedOrder);

      newKanbanData[source.droppableId] = sourceOrders;
      newKanbanData[destination.droppableId] = destOrders;

      setKanbanData(newKanbanData);
      message.success('Etap zlecenia został zaktualizowany');
    } catch (error) {
      message.error('Błąd podczas aktualizacji etapu');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN'
    }).format(amount || 0);
  };

  const formatDate = (date) => {
    if (!date) return null;
    return new Date(date).toLocaleDateString('pl-PL');
  };

  const OrderCard = ({ order, index }) => (
    <Draggable draggableId={order._id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          style={{
            ...provided.draggableProps.style,
            marginBottom: '8px',
          }}
        >
          <Card
            size="small"
            hoverable
            onClick={() => {
              setSelectedOrder(order);
              setModalVisible(true);
            }}
            style={{
              backgroundColor: snapshot.isDragging ? '#f0f0f0' : 'white',
              border: snapshot.isDragging ? '2px dashed #1890ff' : '1px solid #d9d9d9',
            }}
          >
            <div style={{ marginBottom: '8px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span style={{ fontWeight: 'bold', fontSize: '12px' }}>
                  {order.orderNumber}
                </span>
                <Tag color={priorityColors[order.priority]} size="small">
                  {order.priority}
                </Tag>
              </div>
              <div style={{ fontSize: '14px', fontWeight: '500', marginTop: '4px' }}>
                {typeIcons[order.type]} {order.title}
              </div>
            </div>

            <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
              <div>👤 {order.client?.name}</div>
              {order.scheduledDate && (
                <div>📅 {formatDate(order.scheduledDate)}</div>
              )}
              {order.estimatedCost > 0 && (
                <div>💰 {formatCurrency(order.estimatedCost)}</div>
              )}
            </div>

            {order.assignedTechnician && (
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <Avatar size="small" icon={<UserOutlined />} />
                <span style={{ marginLeft: '4px', fontSize: '11px' }}>
                  {order.assignedTechnician.name}
                </span>
              </div>
            )}
          </Card>
        </div>
      )}
    </Draggable>
  );

  const StageColumn = ({ stage }) => (
    <div style={{ 
      width: '280px', 
      marginRight: '16px',
      backgroundColor: stage.color,
      borderRadius: '8px',
      padding: '12px'
    }}>
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        marginBottom: '12px',
        fontWeight: 'bold'
      }}>
        <span style={{ marginRight: '8px', fontSize: '16px' }}>{stage.icon}</span>
        <span>{stage.title}</span>
        <Tag style={{ marginLeft: 'auto' }}>
          {kanbanData[stage.id]?.length || 0}
        </Tag>
      </div>

      <Droppable droppableId={stage.id}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            style={{
              minHeight: '400px',
              backgroundColor: snapshot.isDraggingOver ? '#e6f7ff' : 'transparent',
              borderRadius: '4px',
              padding: '4px',
            }}
          >
            {kanbanData[stage.id]?.map((order, index) => (
              <OrderCard key={order._id} order={order} index={index} />
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );

  if (loading) {
    return <div>Ładowanie...</div>;
  }

  return (
    <div>
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <h2>Kanban Board - Zlecenia Serwisowe</h2>
        <Button type="primary" onClick={fetchKanbanData}>
          Odśwież
        </Button>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <div style={{ 
          display: 'flex', 
          overflowX: 'auto', 
          padding: '16px 0',
          minHeight: '500px'
        }}>
          {stages.map(stage => (
            <StageColumn key={stage.id} stage={stage} />
          ))}
        </div>
      </DragDropContext>

      <Modal
        title={selectedOrder?.title}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedOrder && (
          <div>
            <p><strong>Numer zlecenia:</strong> {selectedOrder.orderNumber}</p>
            <p><strong>Klient:</strong> {selectedOrder.client?.name}</p>
            <p><strong>Opis:</strong> {selectedOrder.description}</p>
            <p><strong>Typ:</strong> {selectedOrder.type}</p>
            <p><strong>Priorytet:</strong> {selectedOrder.priority}</p>
            {selectedOrder.scheduledDate && (
              <p><strong>Zaplanowane:</strong> {formatDate(selectedOrder.scheduledDate)}</p>
            )}
            {selectedOrder.assignedTechnician && (
              <p><strong>Technik:</strong> {selectedOrder.assignedTechnician.name}</p>
            )}
            {selectedOrder.estimatedCost > 0 && (
              <p><strong>Szacowany koszt:</strong> {formatCurrency(selectedOrder.estimatedCost)}</p>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
}
