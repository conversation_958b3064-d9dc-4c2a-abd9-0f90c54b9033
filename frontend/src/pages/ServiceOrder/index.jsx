import React, { useState } from 'react';
import { Ta<PERSON>, But<PERSON> } from 'antd';
import { KanbanOutlined, TableOutlined } from '@ant-design/icons';

import CrudModule from '@/modules/CrudModule';
import DynamicForm from '@/forms/DynamicForm';
import KanbanBoard from './KanbanBoard';
import { fields, dataTableColumns, searchConfig, entityDisplayLabels } from './config';

import useLanguage from '@/locale/useLanguage';

const { TabPane } = Tabs;

export default function ServiceOrder() {
  const translate = useLanguage();
  const entity = 'serviceorder';
  const ENTITY_NAME = 'serviceorder';
  const CREATE_ENTITY = 'create serviceorder';
  const UPDATE_ENTITY = 'update serviceorder';

  const [activeTab, setActiveTab] = useState('kanban');

  const Labels = {
    PANEL_TITLE: translate('Service Orders'),
    DATATABLE_TITLE: translate('service_orders_list'),
    ADD_NEW_ENTITY: translate('add_new_service_order'),
    ENTITY_NAME: translate('service_order'),
    CREATE_ENTITY: translate('create_service_order'),
    UPDATE_ENTITY: translate('update_service_order'),
  };

  const configPage = {
    entity,
    ENTITY_NAME,
    CREATE_ENTITY,
    UPDATE_ENTITY,
    PANEL_TITLE: Labels.PANEL_TITLE,
    dataTableTitle: Labels.DATATABLE_TITLE,
    ENTITY_NAME_TITLE: Labels.ENTITY_NAME,
    entityDisplayLabels,
  };

  const config = {
    ...configPage,
    fields,
    searchConfig,
    dataTableColumns,
    disableAdd: false,
    disableEdit: false,
    disableDelete: false,
    advancedTable: true,
    isUpdateForm: true,
  };

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Zlecenia Serwisowe</h1>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <KanbanOutlined />
                Kanban Board
              </span>
            } 
            key="kanban" 
          />
          <TabPane 
            tab={
              <span>
                <TableOutlined />
                Lista
              </span>
            } 
            key="table" 
          />
        </Tabs>
      </div>

      {activeTab === 'kanban' ? (
        <KanbanBoard />
      ) : (
        <CrudModule
          createForm={<DynamicForm fields={fields} />}
          updateForm={<DynamicForm fields={fields} isUpdateForm={true} />}
          config={config}
        />
      )}
    </div>
  );
}
