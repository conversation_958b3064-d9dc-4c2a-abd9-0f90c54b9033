export const fields = {
  title: {
    type: 'string',
    required: true,
  },
  description: {
    type: 'textarea',
  },
  stage: {
    type: 'select',
    options: [
      { value: 'BACKLOG', label: 'Backlog - Nowe zgłoszenia' },
      { value: 'SCHEDULED', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
      { value: 'IN_PROGRESS', label: 'W trakcie' },
      { value: 'PENDING_PARTS', label: 'Oczekiwanie na części' },
      { value: 'QUALITY_CHECK', label: 'Kontrol<PERSON> jakoś<PERSON>' },
      { value: 'COMPLETED', label: 'Zakończone' },
      { value: 'BILLED', label: 'Rozliczone' },
      { value: 'CLOSED', label: 'Archiwum' },
    ],
  },
  priority: {
    type: 'select',
    options: [
      { value: 'low', label: 'Niski' },
      { value: 'medium', label: 'Średni' },
      { value: 'high', label: 'Wys<PERSON>' },
      { value: 'urgent', label: 'Pilny' },
    ],
  },
  type: {
    type: 'select',
    options: [
      { value: 'maintenance', label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
      { value: 'repair', label: 'Naprawa' },
      { value: 'installation', label: 'Instalacja' },
      { value: 'inspection', label: 'Przegląd' },
      { value: 'emergency', label: 'Awaria' },
    ],
    required: true,
  },
  category: {
    type: 'select',
    options: [
      { value: 'service', label: '🔧 Serwis' },
      { value: 'new_installation', label: '🏗️ Nowa Instalacja' },
      { value: 'inspection', label: '🔍 Oględziny' },
    ],
    required: true,
  },
  scheduledDate: {
    type: 'datetime',
  },
  estimatedDuration: {
    type: 'number',
    min: 0.5,
    max: 24,
    step: 0.5,
  },
  estimatedCost: {
    type: 'currency',
    currency: 'PLN',
  },
  actualCost: {
    type: 'currency',
    currency: 'PLN',
  },
  client: {
    type: 'async',
    entity: 'client',
    displayLabels: ['name'],
    searchFields: ['name', 'email', 'phone'],
    required: true,
  },
  equipment: {
    type: 'async',
    entity: 'equipment',
    displayLabels: ['name', 'model'],
    searchFields: ['name', 'model', 'manufacturer'],
    mode: 'multiple',
  },
  assignedTechnician: {
    type: 'async',
    entity: 'admin',
    displayLabels: ['name'],
    searchFields: ['name', 'email'],
  },
  workPerformed: {
    type: 'textarea',
  },
  issuesFound: {
    type: 'textarea',
  },
  recommendations: {
    type: 'textarea',
  },
  customerSatisfaction: {
    type: 'number',
    min: 1,
    max: 5,
  },
  customerFeedback: {
    type: 'textarea',
  },
};

export const dataTableColumns = [
  {
    title: 'Numer zlecenia',
    dataIndex: 'orderNumber',
    width: 120,
  },
  {
    title: 'Tytuł',
    dataIndex: 'title',
  },
  {
    title: 'Klient',
    dataIndex: ['client', 'name'],
  },
  {
    title: 'Typ',
    dataIndex: 'type',
    render: (type) => {
      const typeLabels = {
        'maintenance': 'Konserwacja',
        'repair': 'Naprawa',
        'installation': 'Instalacja',
        'inspection': 'Przegląd',
        'emergency': 'Awaria',
      };
      return typeLabels[type] || type;
    },
  },
  {
    title: 'Kategoria',
    dataIndex: 'category',
    render: (category) => {
      const categoryLabels = {
        'service': '🔧 Serwis',
        'new_installation': '🏗️ Nowa Instalacja',
        'inspection': '🔍 Oględziny',
      };
      return categoryLabels[category] || category;
    },
  },
  {
    title: 'Etap',
    dataIndex: 'stage',
    render: (stage) => {
      const stageLabels = {
        'BACKLOG': 'Backlog',
        'SCHEDULED': 'Zaplanowane',
        'IN_PROGRESS': 'W trakcie',
        'PENDING_PARTS': 'Oczekiwanie na części',
        'QUALITY_CHECK': 'Kontrola jakości',
        'COMPLETED': 'Zakończone',
        'BILLED': 'Rozliczone',
        'CLOSED': 'Archiwum',
      };
      return stageLabels[stage] || stage;
    },
  },
  {
    title: 'Priorytet',
    dataIndex: 'priority',
    render: (priority) => {
      const colors = {
        'low': 'green',
        'medium': 'orange',
        'high': 'red',
        'urgent': 'purple',
      };
      const labels = {
        'low': 'Niski',
        'medium': 'Średni',
        'high': 'Wysoki',
        'urgent': 'Pilny',
      };
      return <span style={{ color: colors[priority] }}>{labels[priority] || priority}</span>;
    },
  },
  {
    title: 'Zaplanowane',
    dataIndex: 'scheduledDate',
    render: (date) => {
      if (!date) return '-';
      return new Date(date).toLocaleDateString('pl-PL');
    },
  },
  {
    title: 'Technik',
    dataIndex: ['assignedTechnician', 'name'],
    render: (name) => name || 'Nieprzypisany',
  },
];

export const searchConfig = {
  displayLabels: ['title', 'orderNumber'],
  searchFields: ['title', 'orderNumber', 'description'],
  outputValue: '_id',
};

export const entityDisplayLabels = ['title', 'orderNumber'];
