export const fields = {
  name: {
    type: 'string',
    required: true,
  },
  model: {
    type: 'string',
  },
  serialNumber: {
    type: 'string',
  },
  manufacturer: {
    type: 'select',
    options: [
      { value: 'Daikin', label: 'Daikin' },
      { value: 'LG', label: 'LG' },
      { value: 'Mitsubishi', label: 'Mitsubishi' },
      { value: 'Carrier', label: 'Carrier' },
      { value: 'Toshiba', label: 'Toshiba' },
      { value: 'Panasonic', label: 'Panasonic' },
      { value: 'Samsung', label: 'Samsung' },
      { value: 'Fujitsu', label: 'Fujitsu' },
      { value: 'Other', label: 'Inne' },
    ],
    required: true,
  },
  type: {
    type: 'select',
    options: [
      { value: 'air_conditioner', label: 'Klimatyzator' },
      { value: 'heat_pump', label: 'Pompa ciepła' },
      { value: 'furnace', label: 'Piec' },
      { value: 'boiler', label: '<PERSON><PERSON>ł' },
      { value: 'ventilation', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
      { value: 'ductwork', label: '<PERSON><PERSON><PERSON><PERSON>' },
      { value: 'thermostat', label: 'Termostat' },
    ],
    required: true,
  },
  capacity: {
    type: 'string',
  },
  efficiency: {
    type: 'string',
  },
  refrigerantType: {
    type: 'select',
    options: [
      { value: 'R-32', label: 'R-32' },
      { value: 'R-410A', label: 'R-410A' },
      { value: 'R-134a', label: 'R-134a' },
      { value: 'R-22', label: 'R-22' },
      { value: 'R-407C', label: 'R-407C' },
      { value: 'R-290', label: 'R-290' },
    ],
  },
  installationDate: {
    type: 'date',
  },
  warrantyExpiryDate: {
    type: 'date',
  },
  lastMaintenanceDate: {
    type: 'date',
  },
  nextMaintenanceDate: {
    type: 'date',
  },
  location: {
    type: 'string',
  },
  client: {
    type: 'async',
    entity: 'client',
    displayLabels: ['name'],
    searchFields: ['name', 'email'],
    required: true,
  },
  assignedTechnician: {
    type: 'async',
    entity: 'admin',
    displayLabels: ['name'],
    searchFields: ['name', 'email'],
  },
  healthScore: {
    type: 'number',
    min: 0,
    max: 100,
  },
};

export const dataTableColumns = [
  {
    title: 'Nazwa',
    dataIndex: 'name',
  },
  {
    title: 'Model',
    dataIndex: 'model',
  },
  {
    title: 'Producent',
    dataIndex: 'manufacturer',
  },
  {
    title: 'Typ',
    dataIndex: 'type',
    render: (type) => {
      const typeLabels = {
        'air_conditioner': 'Klimatyzator',
        'heat_pump': 'Pompa ciepła',
        'furnace': 'Piec',
        'boiler': 'Kocioł',
        'ventilation': 'Wentylacja',
        'ductwork': 'Kanały',
        'thermostat': 'Termostat',
      };
      return typeLabels[type] || type;
    },
  },
  {
    title: 'Klient',
    dataIndex: ['client', 'name'],
  },
  {
    title: 'Stan zdrowia',
    dataIndex: 'healthScore',
    render: (score) => {
      const color = score >= 80 ? 'green' : score >= 60 ? 'orange' : 'red';
      return <span style={{ color }}>{score}%</span>;
    },
  },
  {
    title: 'Następny serwis',
    dataIndex: 'nextMaintenanceDate',
    render: (date) => {
      if (!date) return '-';
      const maintenanceDate = new Date(date);
      const now = new Date();
      const isOverdue = maintenanceDate < now;
      return (
        <span style={{ color: isOverdue ? 'red' : 'inherit' }}>
          {maintenanceDate.toLocaleDateString('pl-PL')}
        </span>
      );
    },
  },
];

export const searchConfig = {
  displayLabels: ['name', 'model', 'manufacturer'],
  searchFields: ['name', 'model', 'manufacturer', 'serialNumber'],
  outputValue: '_id',
};

export const entityDisplayLabels = ['name', 'model'];
