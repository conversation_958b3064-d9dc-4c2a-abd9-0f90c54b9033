import CrudModule from '@/modules/CrudModule';
import DynamicForm from '@/forms/DynamicForm';
import { fields, dataTableColumns, searchConfig, entityDisplayLabels } from './config';

import useLanguage from '@/locale/useLanguage';

export default function Equipment() {
  const translate = useLanguage();
  const entity = 'equipment';
  const ENTITY_NAME = 'equipment';
  const CREATE_ENTITY = 'create equipment';
  const UPDATE_ENTITY = 'update equipment';

  const Labels = {
    PANEL_TITLE: translate('Equipment'),
    DATATABLE_TITLE: translate('equipment_list'),
    ADD_NEW_ENTITY: translate('add_new_equipment'),
    ENTITY_NAME: translate('equipment'),
    CREATE_ENTITY: translate('create_equipment'),
    UPDATE_ENTITY: translate('update_equipment'),
  };

  const configPage = {
    entity,
    ENTITY_NAME,
    CREATE_ENTITY,
    UPDATE_ENTITY,
    PANEL_TITLE: Labels.PANEL_TITLE,
    dataTableTitle: Labels.DATATABLE_TITLE,
    ENTITY_NAME_TITLE: Labels.ENTITY_NAME,
    entityDisplayLabels,
  };

  const config = {
    ...configPage,
    fields,
    searchConfig,
    dataTableColumns,
    disableAdd: false,
    disableEdit: false,
    disableDelete: false,
    advancedTable: true,
    isUpdateForm: true,
  };

  return (
    <CrudModule
      createForm={<DynamicForm fields={fields} />}
      updateForm={<DynamicForm fields={fields} isUpdateForm={true} />}
      config={config}
    />
  );
}
