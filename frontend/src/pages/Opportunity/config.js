export const fields = {
  name: {
    type: 'string',
    required: true,
  },
  description: {
    type: 'textarea',
  },
  stage: {
    type: 'select',
    options: [
      { value: 'NEW_LEAD', label: 'Nowy Lead' },
      { value: 'QUALIFIED', label: 'Kwalif<PERSON>wany' },
      { value: 'PROPOSAL', label: '<PERSON>po<PERSON><PERSON><PERSON>' },
      { value: 'NEGOTIATION', label: 'Nego<PERSON><PERSON><PERSON><PERSON>' },
      { value: 'IN_PROGRESS', label: 'W Realizacji' },
      { value: 'CLOSED_WON', label: 'Zamknięty - Wygrany' },
      { value: 'FOLLOW_UP', label: 'Follow-up' },
    ],
  },
  value: {
    type: 'currency',
    currency: 'PLN',
    required: true,
  },
  probability: {
    type: 'number',
    min: 0,
    max: 100,
    suffix: '%',
  },
  expectedCloseDate: {
    type: 'date',
  },
  serviceType: {
    type: 'select',
    options: [
      { value: 'installation', label: 'Instalacja' },
      { value: 'maintenance_contract', label: 'Kontrakt serwisowy' },
      { value: 'repair', label: 'Naprawa' },
      { value: 'upgrade', label: 'Modernizacja' },
      { value: 'consultation', label: 'Konsultacja' },
    ],
    required: true,
  },
  equipmentType: {
    type: 'select',
    options: [
      { value: 'air_conditioner', label: 'Klimatyzator' },
      { value: 'heat_pump', label: 'Pompa ciepła' },
      { value: 'furnace', label: 'Piec' },
      { value: 'boiler', label: 'Kocioł' },
      { value: 'ventilation', label: 'Wentylacja' },
      { value: 'complete_system', label: 'Kompletny system' },
    ],
    required: true,
  },
  installationComplexity: {
    type: 'select',
    options: [
      { value: 'simple', label: 'Prosta' },
      { value: 'medium', label: 'Średnia' },
      { value: 'complex', label: 'Złożona' },
      { value: 'very_complex', label: 'Bardzo złożona' },
    ],
  },
  roomCount: {
    type: 'number',
    min: 1,
  },
  totalArea: {
    type: 'number',
    min: 1,
    suffix: 'm²',
  },
  buildingType: {
    type: 'select',
    options: [
      { value: 'residential', label: 'Mieszkaniowy' },
      { value: 'commercial', label: 'Komercyjny' },
      { value: 'industrial', label: 'Przemysłowy' },
      { value: 'office', label: 'Biurowy' },
      { value: 'retail', label: 'Handlowy' },
      { value: 'warehouse', label: 'Magazynowy' },
    ],
  },
  leadSource: {
    type: 'select',
    options: [
      { value: 'website', label: 'Strona internetowa' },
      { value: 'referral', label: 'Polecenie' },
      { value: 'social_media', label: 'Media społecznościowe' },
      { value: 'advertising', label: 'Reklama' },
      { value: 'cold_call', label: 'Cold call' },
      { value: 'trade_show', label: 'Targi' },
      { value: 'other', label: 'Inne' },
    ],
  },
  leadQuality: {
    type: 'select',
    options: [
      { value: 'hot', label: '🔥 Gorący' },
      { value: 'warm', label: '🌡️ Ciepły' },
      { value: 'cold', label: '❄️ Zimny' },
    ],
  },
  client: {
    type: 'async',
    entity: 'client',
    displayLabels: ['name'],
    searchFields: ['name', 'email', 'phone'],
    required: true,
  },
  assignedSalesRep: {
    type: 'async',
    entity: 'admin',
    displayLabels: ['name'],
    searchFields: ['name', 'email'],
  },
  competitors: {
    type: 'tags',
  },
  competitiveAdvantage: {
    type: 'textarea',
  },
  lastContactDate: {
    type: 'date',
  },
  nextFollowUpDate: {
    type: 'date',
  },
};

export const dataTableColumns = [
  {
    title: 'Nazwa',
    dataIndex: 'name',
  },
  {
    title: 'Klient',
    dataIndex: ['client', 'name'],
  },
  {
    title: 'Wartość',
    dataIndex: 'value',
    render: (value) => {
      return new Intl.NumberFormat('pl-PL', {
        style: 'currency',
        currency: 'PLN'
      }).format(value || 0);
    },
  },
  {
    title: 'Prawdopodobieństwo',
    dataIndex: 'probability',
    render: (probability) => `${probability}%`,
  },
  {
    title: 'Etap',
    dataIndex: 'stage',
    render: (stage) => {
      const stageLabels = {
        'NEW_LEAD': 'Nowy Lead',
        'QUALIFIED': 'Kwalifikowany',
        'PROPOSAL': 'Propozycja',
        'NEGOTIATION': 'Negocjacje',
        'IN_PROGRESS': 'W Realizacji',
        'CLOSED_WON': 'Zamknięty - Wygrany',
        'FOLLOW_UP': 'Follow-up',
      };
      return stageLabels[stage] || stage;
    },
  },
  {
    title: 'Typ usługi',
    dataIndex: 'serviceType',
    render: (type) => {
      const typeLabels = {
        'installation': 'Instalacja',
        'maintenance_contract': 'Kontrakt serwisowy',
        'repair': 'Naprawa',
        'upgrade': 'Modernizacja',
        'consultation': 'Konsultacja',
      };
      return typeLabels[type] || type;
    },
  },
  {
    title: 'Lead Score',
    dataIndex: 'leadScore',
    render: (score) => {
      const color = score >= 80 ? 'green' : score >= 60 ? 'orange' : 'red';
      return <span style={{ color }}>{score}</span>;
    },
  },
  {
    title: 'Oczekiwane zamknięcie',
    dataIndex: 'expectedCloseDate',
    render: (date) => {
      if (!date) return '-';
      return new Date(date).toLocaleDateString('pl-PL');
    },
  },
  {
    title: 'Handlowiec',
    dataIndex: ['assignedSalesRep', 'name'],
    render: (name) => name || 'Nieprzypisany',
  },
];

export const searchConfig = {
  displayLabels: ['name'],
  searchFields: ['name', 'description'],
  outputValue: '_id',
};

export const entityDisplayLabels = ['name'];
