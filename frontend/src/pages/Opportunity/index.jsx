import React, { useState } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'antd';
import { FunnelPlotOutlined, TableOutlined } from '@ant-design/icons';

import CrudModule from '@/modules/CrudModule';
import DynamicForm from '@/forms/DynamicForm';
import SalesPipeline from './SalesPipeline';
import { fields, dataTableColumns, searchConfig, entityDisplayLabels } from './config';

import useLanguage from '@/locale/useLanguage';

const { TabPane } = Tabs;

export default function Opportunity() {
  const translate = useLanguage();
  const entity = 'opportunity';
  const ENTITY_NAME = 'opportunity';
  const CREATE_ENTITY = 'create opportunity';
  const UPDATE_ENTITY = 'update opportunity';

  const [activeTab, setActiveTab] = useState('pipeline');

  const Labels = {
    PANEL_TITLE: translate('Opportunities'),
    DATATABLE_TITLE: translate('opportunities_list'),
    ADD_NEW_ENTITY: translate('add_new_opportunity'),
    ENTITY_NAME: translate('opportunity'),
    CREATE_ENTITY: translate('create_opportunity'),
    UPDATE_ENTITY: translate('update_opportunity'),
  };

  const configPage = {
    entity,
    ENTITY_NAME,
    CREATE_ENTITY,
    UPDATE_ENTITY,
    PANEL_TITLE: Labels.PANEL_TITLE,
    dataTableTitle: Labels.DATATABLE_TITLE,
    ENTITY_NAME_TITLE: Labels.ENTITY_NAME,
    entityDisplayLabels,
  };

  const config = {
    ...configPage,
    fields,
    searchConfig,
    dataTableColumns,
    disableAdd: false,
    disableEdit: false,
    disableDelete: false,
    advancedTable: true,
    isUpdateForm: true,
  };

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Możliwości Sprzedaży</h1>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <FunnelPlotOutlined />
                Sales Pipeline
              </span>
            } 
            key="pipeline" 
          />
          <TabPane 
            tab={
              <span>
                <TableOutlined />
                Lista
              </span>
            } 
            key="table" 
          />
        </Tabs>
      </div>

      {activeTab === 'pipeline' ? (
        <SalesPipeline />
      ) : (
        <CrudModule
          createForm={<DynamicForm fields={fields} />}
          updateForm={<DynamicForm fields={fields} isUpdateForm={true} />}
          config={config}
        />
      )}
    </div>
  );
}
