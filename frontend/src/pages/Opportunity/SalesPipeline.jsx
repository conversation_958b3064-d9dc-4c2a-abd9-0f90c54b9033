import React, { useState, useEffect } from 'react';
import { Card, Tag, Avatar, Tooltip, Button, Modal, message, Progress } from 'antd';
import { UserOutlined, CalendarOutlined, DollarOutlined, TrophyOutlined } from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

import { request } from '@/request';
import useLanguage from '@/locale/useLanguage';

const stages = [
  { id: 'NEW_LEAD', title: 'Nowy Lead', color: '#f0f0f0', icon: '🆕', probability: 10 },
  { id: 'QUALIFIED', title: 'Kwalifikowany', color: '#e6f7ff', icon: '✅', probability: 25 },
  { id: 'PROPOSAL', title: 'Propozycja', color: '#fff2e6', icon: '📋', probability: 50 },
  { id: 'NEGOTIATION', title: 'Nego<PERSON><PERSON><PERSON>je', color: '#fff1f0', icon: '🤝', probability: 75 },
  { id: 'IN_PROGRESS', title: 'W Realizacji', color: '#f6ffed', icon: '🚀', probability: 90 },
  { id: 'CLOSED_WON', title: '<PERSON><PERSON><PERSON><PERSON>ęty - Wygrany', color: '#f0f9ff', icon: '🏆', probability: 100 },
  { id: 'FOLLOW_UP', title: 'Follow-up', color: '#f9f0ff', icon: '🔄', probability: 15 },
];

const leadQualityColors = {
  hot: '#ff4d4f',
  warm: '#faad14',
  cold: '#1890ff',
};

const serviceTypeIcons = {
  installation: '🏗️',
  maintenance_contract: '🔧',
  repair: '⚡',
  upgrade: '⬆️',
  consultation: '💡',
};

export default function SalesPipeline() {
  const translate = useLanguage();
  const [pipelineData, setPipelineData] = useState({});
  const [loading, setLoading] = useState(true);
  const [selectedOpportunity, setSelectedOpportunity] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [totalValue, setTotalValue] = useState(0);

  useEffect(() => {
    fetchPipelineData();
  }, []);

  const fetchPipelineData = async () => {
    try {
      setLoading(true);
      const response = await request.get({ entity: 'opportunity/pipeline' });
      if (response.success) {
        setPipelineData(response.result);
        calculateTotalValue(response.result);
      }
    } catch (error) {
      message.error('Błąd podczas ładowania danych pipeline');
    } finally {
      setLoading(false);
    }
  };

  const calculateTotalValue = (data) => {
    let total = 0;
    Object.values(data).forEach(opportunities => {
      opportunities.forEach(opp => {
        total += (opp.value || 0) * (opp.probability || 0) / 100;
      });
    });
    setTotalValue(total);
  };

  const handleDragEnd = async (result) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) return;

    try {
      // Update stage in backend
      await request.patch({
        entity: `opportunity/stage/${draggableId}`,
        jsonData: { stage: destination.droppableId }
      });

      // Update local state
      const newPipelineData = { ...pipelineData };
      const sourceOpportunities = [...newPipelineData[source.droppableId]];
      const destOpportunities = source.droppableId === destination.droppableId 
        ? sourceOpportunities 
        : [...newPipelineData[destination.droppableId]];

      const [movedOpportunity] = sourceOpportunities.splice(source.index, 1);
      movedOpportunity.stage = destination.droppableId;
      
      // Update probability based on stage
      const newStage = stages.find(s => s.id === destination.droppableId);
      if (newStage) {
        movedOpportunity.probability = newStage.probability;
      }
      
      destOpportunities.splice(destination.index, 0, movedOpportunity);

      newPipelineData[source.droppableId] = sourceOpportunities;
      newPipelineData[destination.droppableId] = destOpportunities;

      setPipelineData(newPipelineData);
      calculateTotalValue(newPipelineData);
      message.success('Etap możliwości został zaktualizowany');
    } catch (error) {
      message.error('Błąd podczas aktualizacji etapu');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN'
    }).format(amount || 0);
  };

  const formatDate = (date) => {
    if (!date) return null;
    return new Date(date).toLocaleDateString('pl-PL');
  };

  const OpportunityCard = ({ opportunity, index }) => (
    <Draggable draggableId={opportunity._id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          style={{
            ...provided.draggableProps.style,
            marginBottom: '8px',
          }}
        >
          <Card
            size="small"
            hoverable
            onClick={() => {
              setSelectedOpportunity(opportunity);
              setModalVisible(true);
            }}
            style={{
              backgroundColor: snapshot.isDragging ? '#f0f0f0' : 'white',
              border: snapshot.isDragging ? '2px dashed #1890ff' : '1px solid #d9d9d9',
            }}
          >
            <div style={{ marginBottom: '8px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span style={{ fontWeight: 'bold', fontSize: '14px' }}>
                  {opportunity.name}
                </span>
                <Tag color={leadQualityColors[opportunity.leadQuality]} size="small">
                  {opportunity.leadQuality}
                </Tag>
              </div>
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                {serviceTypeIcons[opportunity.serviceType]} {opportunity.serviceType}
              </div>
            </div>

            <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
              <div>👤 {opportunity.client?.name}</div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '4px' }}>
                <span>💰 {formatCurrency(opportunity.value)}</span>
                <span>{opportunity.probability}%</span>
              </div>
              {opportunity.expectedCloseDate && (
                <div>📅 {formatDate(opportunity.expectedCloseDate)}</div>
              )}
            </div>

            <div style={{ marginBottom: '8px' }}>
              <Progress 
                percent={opportunity.leadScore || 50} 
                size="small" 
                strokeColor={opportunity.leadScore >= 80 ? '#52c41a' : opportunity.leadScore >= 60 ? '#faad14' : '#ff4d4f'}
                format={() => `${opportunity.leadScore || 50}`}
              />
              <div style={{ fontSize: '10px', color: '#999', textAlign: 'center' }}>
                Lead Score
              </div>
            </div>

            {opportunity.assignedSalesRep && (
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <Avatar size="small" icon={<UserOutlined />} />
                <span style={{ marginLeft: '4px', fontSize: '11px' }}>
                  {opportunity.assignedSalesRep.name}
                </span>
              </div>
            )}
          </Card>
        </div>
      )}
    </Draggable>
  );

  const StageColumn = ({ stage }) => {
    const stageOpportunities = pipelineData[stage.id] || [];
    const stageValue = stageOpportunities.reduce((sum, opp) => sum + (opp.value || 0), 0);
    const weightedValue = stageOpportunities.reduce((sum, opp) => sum + (opp.value || 0) * (opp.probability || 0) / 100, 0);

    return (
      <div style={{ 
        width: '280px', 
        marginRight: '16px',
        backgroundColor: stage.color,
        borderRadius: '8px',
        padding: '12px'
      }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: '8px',
          fontWeight: 'bold'
        }}>
          <span style={{ marginRight: '8px', fontSize: '16px' }}>{stage.icon}</span>
          <span style={{ fontSize: '12px' }}>{stage.title}</span>
          <Tag style={{ marginLeft: 'auto' }}>
            {stageOpportunities.length}
          </Tag>
        </div>

        <div style={{ fontSize: '10px', color: '#666', marginBottom: '12px' }}>
          <div>Wartość: {formatCurrency(stageValue)}</div>
          <div>Ważona: {formatCurrency(weightedValue)}</div>
        </div>

        <Droppable droppableId={stage.id}>
          {(provided, snapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              style={{
                minHeight: '400px',
                backgroundColor: snapshot.isDraggingOver ? '#e6f7ff' : 'transparent',
                borderRadius: '4px',
                padding: '4px',
              }}
            >
              {stageOpportunities.map((opportunity, index) => (
                <OpportunityCard key={opportunity._id} opportunity={opportunity} index={index} />
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </div>
    );
  };

  if (loading) {
    return <div>Ładowanie...</div>;
  }

  return (
    <div>
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <h2>Sales Pipeline</h2>
          <div style={{ fontSize: '14px', color: '#666' }}>
            Całkowita ważona wartość pipeline: <strong>{formatCurrency(totalValue)}</strong>
          </div>
        </div>
        <Button type="primary" onClick={fetchPipelineData}>
          Odśwież
        </Button>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <div style={{ 
          display: 'flex', 
          overflowX: 'auto', 
          padding: '16px 0',
          minHeight: '500px'
        }}>
          {stages.map(stage => (
            <StageColumn key={stage.id} stage={stage} />
          ))}
        </div>
      </DragDropContext>

      <Modal
        title={selectedOpportunity?.name}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedOpportunity && (
          <div>
            <p><strong>Klient:</strong> {selectedOpportunity.client?.name}</p>
            <p><strong>Opis:</strong> {selectedOpportunity.description}</p>
            <p><strong>Wartość:</strong> {formatCurrency(selectedOpportunity.value)}</p>
            <p><strong>Prawdopodobieństwo:</strong> {selectedOpportunity.probability}%</p>
            <p><strong>Lead Score:</strong> {selectedOpportunity.leadScore}</p>
            <p><strong>Typ usługi:</strong> {selectedOpportunity.serviceType}</p>
            <p><strong>Typ sprzętu:</strong> {selectedOpportunity.equipmentType}</p>
            {selectedOpportunity.expectedCloseDate && (
              <p><strong>Oczekiwane zamknięcie:</strong> {formatDate(selectedOpportunity.expectedCloseDate)}</p>
            )}
            {selectedOpportunity.assignedSalesRep && (
              <p><strong>Handlowiec:</strong> {selectedOpportunity.assignedSalesRep.name}</p>
            )}
            {selectedOpportunity.leadSource && (
              <p><strong>Źródło leada:</strong> {selectedOpportunity.leadSource}</p>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
}
