require('dotenv').config({ path: '.env' });
require('module-alias/register');

const mongoose = require('mongoose');
const { generate: uniqueId } = require('shortid');

// Connect to database
mongoose.connect(process.env.DATABASE);

async function createTestAdmin() {
  try {
    const Admin = require('./src/models/coreModels/Admin');
    const AdminPassword = require('./src/models/coreModels/AdminPassword');
    
    const newAdminPassword = new AdminPassword();
    
    const salt = uniqueId();
    const passwordHash = newAdminPassword.generateHash(salt, 'testpass123');
    
    const testAdmin = {
      email: '<EMAIL>',
      name: 'Test',
      surname: 'Admin',
      enabled: true,
      role: 'owner',
    };
    
    const result = await new Admin(testAdmin).save();
    
    const AdminPasswordData = {
      password: passwordHash,
      emailVerified: true,
      salt: salt,
      user: result._id,
    };
    
    await new AdminPassword(AdminPasswordData).save();
    
    console.log('✅ Test admin created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: testpass123');
    
    process.exit();
  } catch (error) {
    console.error('❌ Error creating test admin:', error);
    process.exit(1);
  }
}

createTestAdmin();
