const express = require('express');
const { catchErrors } = require('@/handlers/errorHandlers');
const router = express.Router();

const appControllers = require('@/controllers/appControllers');
const { routesList } = require('@/models/utils');

const routerApp = (entity, controller) => {
  router.route(`/${entity}/create`).post(catchErrors(controller['create']));
  router.route(`/${entity}/read/:id`).get(catchErrors(controller['read']));
  router.route(`/${entity}/update/:id`).patch(catchErrors(controller['update']));
  router.route(`/${entity}/delete/:id`).delete(catchErrors(controller['delete']));
  router.route(`/${entity}/search`).get(catchErrors(controller['search']));
  router.route(`/${entity}/list`).get(catchErrors(controller['list']));
  router.route(`/${entity}/listAll`).get(catchErrors(controller['listAll']));
  router.route(`/${entity}/filter`).get(catchErrors(controller['filter']));
  router.route(`/${entity}/summary`).get(catchErrors(controller['summary']));

  if (entity === 'invoice' || entity === 'quote' || entity === 'payment') {
    router.route(`/${entity}/mail`).post(catchErrors(controller['mail']));
  }

  if (entity === 'quote') {
    router.route(`/${entity}/convert/:id`).get(catchErrors(controller['convert']));
  }

  // HVAC-specific routes
  if (entity === 'equipment') {
    router.route(`/${entity}/client/:clientId`).get(catchErrors(controller['getByClient']));
    router.route(`/${entity}/health/:id`).patch(catchErrors(controller['updateHealthScore']));
  }

  if (entity === 'serviceorder') {
    router.route(`/${entity}/technician/:technicianId`).get(catchErrors(controller['getByTechnician']));
    router.route(`/${entity}/stage/:id`).patch(catchErrors(controller['updateStage']));
    router.route(`/${entity}/kanban`).get(catchErrors(controller['getKanbanBoard']));
  }

  if (entity === 'opportunity') {
    router.route(`/${entity}/pipeline`).get(catchErrors(controller['getSalesPipeline']));
    router.route(`/${entity}/stage/:id`).patch(catchErrors(controller['updateStage']));
  }
};

routesList.forEach(({ entity, controllerName }) => {
  const controller = appControllers[controllerName];
  routerApp(entity, controller);
});

module.exports = router;
