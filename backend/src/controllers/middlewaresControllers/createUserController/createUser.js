const Joi = require('joi');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { generate: uniqueId } = require('shortid');

const createUser = async (userModel, req, res) => {
  const User = mongoose.model(userModel);
  const UserPassword = mongoose.model(userModel + 'Password');

  const { name, surname, email, password, role = 'owner' } = req.body;

  // Validation schema
  const objectSchema = Joi.object({
    name: Joi.string().required(),
    surname: Joi.string().allow(''),
    email: Joi.string()
      .email({ tlds: { allow: true } })
      .required(),
    password: Joi.string().min(8).required(),
    role: Joi.string().valid('owner', 'admin', 'manager', 'employee', 'create_only', 'read_only').default('owner'),
  });

  const { error, value } = objectSchema.validate({ name, surname, email, password, role });
  if (error) {
    return res.status(409).json({
      success: false,
      result: null,
      error: error,
      message: 'Invalid/Missing credentials.',
      errorMessage: error.message,
    });
  }

  try {
    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase(), removed: false });
    if (existingUser) {
      return res.status(409).json({
        success: false,
        result: null,
        message: 'User with this email already exists.',
      });
    }

    // Create new user
    const userData = {
      email: email.toLowerCase(),
      name,
      surname: surname || '',
      role,
      enabled: true,
      removed: false,
    };

    const newUser = await new User(userData).save();

    // Create password entry
    const salt = uniqueId();
    const hashedPassword = bcrypt.hashSync(salt + password);

    const userPasswordData = {
      password: hashedPassword,
      emailVerified: true,
      salt: salt,
      user: newUser._id,
      removed: false,
    };

    await new UserPassword(userPasswordData).save();

    // Return success response without sensitive data
    const result = {
      _id: newUser._id,
      email: newUser.email,
      name: newUser.name,
      surname: newUser.surname,
      role: newUser.role,
      enabled: newUser.enabled,
      created: newUser.created,
    };

    return res.status(200).json({
      success: true,
      result,
      message: 'User created successfully',
    });

  } catch (error) {
    console.error('Error creating user:', error);
    return res.status(500).json({
      success: false,
      result: null,
      message: 'Internal server error while creating user.',
      error: error.message,
    });
  }
};

module.exports = createUser;
