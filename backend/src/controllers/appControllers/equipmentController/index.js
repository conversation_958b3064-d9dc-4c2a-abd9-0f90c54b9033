const createCRUDController = require('@/controllers/middlewaresControllers/createCRUDController');
const { calculate } = require('@/helpers');

const Equipment = require('@/models/appModels/Equipment');

const equipmentController = createCRUDController('Equipment');

// Override methods for HVAC-specific functionality
equipmentController.create = async (req, res) => {
  try {
    const { body } = req;
    
    // Generate QR code if not provided
    if (!body.qrCode) {
      body.qrCode = `EQ-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // Set initial health score
    if (!body.healthScore) {
      body.healthScore = 100;
    }
    
    // Calculate next maintenance date if installation date provided
    if (body.installationDate && !body.nextMaintenanceDate) {
      const installDate = new Date(body.installationDate);
      const nextMaintenance = new Date(installDate);
      nextMaintenance.setMonth(nextMaintenance.getMonth() + 6); // 6 months default
      body.nextMaintenanceDate = nextMaintenance;
    }
    
    const result = await Equipment.create(body);
    return res.status(200).json({
      success: true,
      result,
      message: 'Equipment created successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

equipmentController.summary = async (req, res) => {
  try {
    const Equipment = require('@/models/appModels/Equipment');
    
    // Get equipment statistics
    const totalEquipment = await Equipment.countDocuments({ removed: false });
    const activeEquipment = await Equipment.countDocuments({ 
      removed: false, 
      enabled: true 
    });
    
    // Equipment by type
    const equipmentByType = await Equipment.aggregate([
      { $match: { removed: false } },
      { $group: { _id: '$type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Equipment by manufacturer
    const equipmentByManufacturer = await Equipment.aggregate([
      { $match: { removed: false } },
      { $group: { _id: '$manufacturer', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Health score distribution
    const healthScoreStats = await Equipment.aggregate([
      { $match: { removed: false } },
      {
        $group: {
          _id: null,
          avgHealthScore: { $avg: '$healthScore' },
          minHealthScore: { $min: '$healthScore' },
          maxHealthScore: { $max: '$healthScore' }
        }
      }
    ]);
    
    // Equipment needing maintenance (next 30 days)
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    
    const maintenanceDue = await Equipment.countDocuments({
      removed: false,
      nextMaintenanceDate: { $lte: thirtyDaysFromNow }
    });
    
    // Equipment with low health scores (< 50)
    const lowHealthEquipment = await Equipment.countDocuments({
      removed: false,
      healthScore: { $lt: 50 }
    });
    
    return res.status(200).json({
      success: true,
      result: {
        totalEquipment,
        activeEquipment,
        equipmentByType,
        equipmentByManufacturer,
        healthScoreStats: healthScoreStats[0] || {},
        maintenanceDue,
        lowHealthEquipment
      },
      message: 'Equipment summary retrieved successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

// Get equipment by client
equipmentController.getByClient = async (req, res) => {
  try {
    const { clientId } = req.params;
    
    const equipment = await Equipment.find({ 
      client: clientId, 
      removed: false 
    }).populate('assignedTechnician', 'name email');
    
    return res.status(200).json({
      success: true,
      result: equipment,
      message: 'Client equipment retrieved successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

// Update health score
equipmentController.updateHealthScore = async (req, res) => {
  try {
    const { id } = req.params;
    const { healthScore, aiInsights } = req.body;
    
    const equipment = await Equipment.findByIdAndUpdate(
      id,
      { 
        healthScore,
        aiInsights,
        updated: new Date()
      },
      { new: true }
    );
    
    if (!equipment) {
      return res.status(404).json({
        success: false,
        result: null,
        message: 'Equipment not found',
      });
    }
    
    return res.status(200).json({
      success: true,
      result: equipment,
      message: 'Equipment health score updated successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

module.exports = equipmentController;
