const createCRUDController = require('@/controllers/middlewaresControllers/createCRUDController');
const { calculate } = require('@/helpers');

const Opportunity = require('@/models/appModels/Opportunity');

const opportunityController = createCRUDController('Opportunity');

// Override methods for HVAC-specific functionality
opportunityController.create = async (req, res) => {
  try {
    const { body } = req;
    
    // Auto-calculate lead score based on various factors
    if (!body.leadScore) {
      let score = 50; // Base score
      
      // Adjust based on lead source
      const sourceScores = {
        'referral': 20,
        'website': 15,
        'social_media': 10,
        'advertising': 5,
        'cold_call': -5,
        'trade_show': 10,
        'other': 0
      };
      score += sourceScores[body.leadSource] || 0;
      
      // Adjust based on value
      if (body.value > 50000) score += 20;
      else if (body.value > 20000) score += 10;
      else if (body.value > 10000) score += 5;
      
      // Adjust based on building type
      if (body.buildingType === 'commercial') score += 15;
      else if (body.buildingType === 'industrial') score += 10;
      
      body.leadScore = Math.min(100, Math.max(0, score));
    }
    
    // Set probability based on stage
    if (!body.probability) {
      const stageProbabilities = {
        'NEW_LEAD': 10,
        'QUALIFIED': 25,
        'PROPOSAL': 50,
        'NEGOTIATION': 75,
        'IN_PROGRESS': 90,
        'CLOSED_WON': 100,
        'FOLLOW_UP': 15
      };
      body.probability = stageProbabilities[body.stage] || 10;
    }
    
    const result = await Opportunity.create(body);
    return res.status(200).json({
      success: true,
      result,
      message: 'Opportunity created successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

opportunityController.summary = async (req, res) => {
  try {
    const Opportunity = require('@/models/appModels/Opportunity');
    
    // Get opportunity statistics
    const totalOpportunities = await Opportunity.countDocuments({ removed: false });
    const activeOpportunities = await Opportunity.countDocuments({ 
      removed: false, 
      stage: { $ne: 'CLOSED_WON' }
    });
    
    // Opportunities by stage
    const opportunitiesByStage = await Opportunity.aggregate([
      { $match: { removed: false } },
      { $group: { _id: '$stage', count: { $sum: 1 }, totalValue: { $sum: '$value' } } },
      { $sort: { count: -1 } }
    ]);
    
    // Opportunities by service type
    const opportunitiesByServiceType = await Opportunity.aggregate([
      { $match: { removed: false } },
      { $group: { _id: '$serviceType', count: { $sum: 1 }, totalValue: { $sum: '$value' } } },
      { $sort: { count: -1 } }
    ]);
    
    // Lead source analysis
    const opportunitiesByLeadSource = await Opportunity.aggregate([
      { $match: { removed: false } },
      { $group: { _id: '$leadSource', count: { $sum: 1 }, totalValue: { $sum: '$value' } } },
      { $sort: { count: -1 } }
    ]);
    
    // Revenue statistics
    const revenueStats = await Opportunity.aggregate([
      { $match: { removed: false } },
      {
        $group: {
          _id: null,
          totalPipelineValue: { $sum: '$value' },
          avgOpportunityValue: { $avg: '$value' },
          minOpportunityValue: { $min: '$value' },
          maxOpportunityValue: { $max: '$value' },
          avgLeadScore: { $avg: '$leadScore' },
          avgProbability: { $avg: '$probability' }
        }
      }
    ]);
    
    // Won opportunities this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);
    
    const wonThisMonth = await Opportunity.aggregate([
      { 
        $match: { 
          removed: false,
          stage: 'CLOSED_WON',
          actualCloseDate: { $gte: startOfMonth }
        }
      },
      {
        $group: {
          _id: null,
          count: { $sum: 1 },
          totalValue: { $sum: '$value' }
        }
      }
    ]);
    
    // Hot leads (high score and probability)
    const hotLeads = await Opportunity.countDocuments({
      removed: false,
      leadScore: { $gte: 80 },
      probability: { $gte: 70 },
      stage: { $ne: 'CLOSED_WON' }
    });
    
    return res.status(200).json({
      success: true,
      result: {
        totalOpportunities,
        activeOpportunities,
        opportunitiesByStage,
        opportunitiesByServiceType,
        opportunitiesByLeadSource,
        revenueStats: revenueStats[0] || {},
        wonThisMonth: wonThisMonth[0] || { count: 0, totalValue: 0 },
        hotLeads
      },
      message: 'Opportunity summary retrieved successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

// Get sales pipeline data
opportunityController.getSalesPipeline = async (req, res) => {
  try {
    const stages = [
      'NEW_LEAD', 'QUALIFIED', 'PROPOSAL', 'NEGOTIATION', 
      'IN_PROGRESS', 'CLOSED_WON', 'FOLLOW_UP'
    ];
    
    const pipelineData = {};
    
    for (const stage of stages) {
      const opportunities = await Opportunity.find({ 
        stage, 
        removed: false 
      })
      .populate('client', 'name phone email')
      .populate('assignedSalesRep', 'name')
      .sort({ leadScore: -1, value: -1 });
      
      pipelineData[stage] = opportunities;
    }
    
    return res.status(200).json({
      success: true,
      result: pipelineData,
      message: 'Sales pipeline data retrieved successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

// Update stage
opportunityController.updateStage = async (req, res) => {
  try {
    const { id } = req.params;
    const { stage, notes } = req.body;
    
    const updateData = { 
      stage,
      updated: new Date()
    };
    
    // Auto-update probability based on stage
    const stageProbabilities = {
      'NEW_LEAD': 10,
      'QUALIFIED': 25,
      'PROPOSAL': 50,
      'NEGOTIATION': 75,
      'IN_PROGRESS': 90,
      'CLOSED_WON': 100,
      'FOLLOW_UP': 15
    };
    updateData.probability = stageProbabilities[stage] || 10;
    
    // Set close date when won
    if (stage === 'CLOSED_WON') {
      updateData.actualCloseDate = new Date();
    }
    
    const opportunity = await Opportunity.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    );
    
    if (!opportunity) {
      return res.status(404).json({
        success: false,
        result: null,
        message: 'Opportunity not found',
      });
    }
    
    return res.status(200).json({
      success: true,
      result: opportunity,
      message: 'Opportunity stage updated successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

module.exports = opportunityController;
