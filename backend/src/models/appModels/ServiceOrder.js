const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  removed: {
    type: Boolean,
    default: false,
  },
  enabled: {
    type: Boolean,
    default: true,
  },

  // Basic Service Order Information
  title: {
    type: String,
    required: true,
  },
  description: String,
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  
  // 8-Stage HVAC Workflow
  stage: {
    type: String,
    enum: [
      'BACKLOG',           // Nowe zgłoszenia
      'SCHEDULED',         // Zaplanowane
      'IN_PROGRESS',       // W trakcie
      'PENDING_PARTS',     // Oczekiwanie na części
      'QUALITY_CHECK',     // Kontrola jakości
      'COMPLETED',         // Zakończone
      'BILLED',           // Rozliczone
      'CLOSED'            // Archiwum
    ],
    default: 'BACKLOG'
  },
  
  // Service Details
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  type: {
    type: String,
    enum: ['maintenance', 'repair', 'installation', 'inspection', 'emergency'],
    required: true
  },
  category: {
    type: String,
    enum: ['service', 'new_installation', 'inspection'], // 3 calendar categories
    required: true
  },
  
  // Scheduling
  scheduledDate: Date,
  estimatedDuration: Number, // in hours
  actualStartTime: Date,
  actualEndTime: Date,
  completedDate: Date,
  
  // Financial
  estimatedCost: {
    type: Number,
    default: 0
  },
  actualCost: {
    type: Number,
    default: 0
  },
  currency: {
    type: String,
    default: 'PLN'
  },
  
  // Parts & Materials
  partsRequired: [{
    name: String,
    quantity: Number,
    cost: Number,
    ordered: { type: Boolean, default: false },
    received: { type: Boolean, default: false }
  }],
  
  // Work Details
  workPerformed: String,
  issuesFound: String,
  recommendations: String,
  
  // Quality & Completion
  qualityCheckPassed: {
    type: Boolean,
    default: false
  },
  customerSatisfaction: {
    type: Number,
    min: 1,
    max: 5
  },
  customerFeedback: String,
  
  // Digital Protocol
  protocolSigned: {
    type: Boolean,
    default: false
  },
  digitalSignature: String,
  protocolPhotos: [String], // URLs to photos
  
  // Relationships
  client: { 
    type: mongoose.Schema.ObjectId, 
    ref: 'Client',
    required: true
  },
  equipment: [{ type: mongoose.Schema.ObjectId, ref: 'Equipment' }],
  assignedTechnician: { type: mongoose.Schema.ObjectId, ref: 'Admin' },
  invoice: { type: mongoose.Schema.ObjectId, ref: 'Invoice' },
  
  // AI & Analytics
  aiInsights: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  // Metadata
  createdBy: { type: mongoose.Schema.ObjectId, ref: 'Admin' },
  created: {
    type: Date,
    default: Date.now,
  },
  updated: {
    type: Date,
    default: Date.now,
  },
});

// Auto-generate order number
schema.pre('save', async function(next) {
  if (!this.orderNumber) {
    const count = await mongoose.model('ServiceOrder').countDocuments();
    this.orderNumber = `SO-${new Date().getFullYear()}-${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Indexes for performance
schema.index({ client: 1 });
schema.index({ stage: 1 });
schema.index({ assignedTechnician: 1 });
schema.index({ scheduledDate: 1 });
schema.index({ type: 1 });
schema.index({ category: 1 });

schema.plugin(require('mongoose-autopopulate'));

module.exports = mongoose.model('ServiceOrder', schema);
