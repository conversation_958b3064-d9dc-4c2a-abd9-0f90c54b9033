const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  removed: {
    type: Boolean,
    default: false,
  },
  enabled: {
    type: Boolean,
    default: true,
  },

  // Basic Opportunity Information
  name: {
    type: String,
    required: true,
  },
  description: String,
  
  // 7-Stage Sales Pipeline
  stage: {
    type: String,
    enum: [
      'NEW_LEAD',          // Nowy Lead
      'QUALIFIED',         // Kwalifikowany
      'PROPOSAL',          // Propozy<PERSON>ja
      'NEGOTIATION',       // Negocjacje
      'IN_PROGRESS',       // W Realizacji
      'CLOSED_WON',        // Zamknięty - Wygrany
      'FOLLOW_UP'          // Follow-up
    ],
    default: 'NEW_LEAD'
  },
  
  // Financial Information
  value: {
    type: Number,
    required: true,
    default: 0
  },
  currency: {
    type: String,
    default: 'PLN'
  },
  probability: {
    type: Number,
    min: 0,
    max: 100,
    default: 10
  },
  
  // Dates
  expectedCloseDate: Date,
  actualCloseDate: Date,
  
  // HVAC-Specific Fields
  serviceType: {
    type: String,
    enum: ['installation', 'maintenance_contract', 'repair', 'upgrade', 'consultation'],
    required: true
  },
  equipmentType: {
    type: String,
    enum: ['air_conditioner', 'heat_pump', 'furnace', 'boiler', 'ventilation', 'complete_system'],
    required: true
  },
  installationComplexity: {
    type: String,
    enum: ['simple', 'medium', 'complex', 'very_complex'],
    default: 'medium'
  },
  
  // Project Details
  roomCount: Number,
  totalArea: Number, // in square meters
  buildingType: {
    type: String,
    enum: ['residential', 'commercial', 'industrial', 'office', 'retail', 'warehouse'],
    default: 'residential'
  },
  
  // Lead Information
  leadSource: {
    type: String,
    enum: ['website', 'referral', 'social_media', 'advertising', 'cold_call', 'trade_show', 'other'],
    default: 'website'
  },
  leadQuality: {
    type: String,
    enum: ['hot', 'warm', 'cold'],
    default: 'warm'
  },
  
  // AI-Powered Features
  leadScore: {
    type: Number,
    min: 0,
    max: 100,
    default: 50
  },
  aiInsights: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  churnRisk: {
    type: Number,
    min: 0,
    max: 1,
    default: 0
  },
  
  // Competition & Market
  competitors: [String],
  competitiveAdvantage: String,
  
  // Communication
  lastContactDate: Date,
  nextFollowUpDate: Date,
  communicationHistory: [{
    date: Date,
    type: { type: String, enum: ['call', 'email', 'meeting', 'site_visit'] },
    notes: String,
    outcome: String
  }],
  
  // Relationships
  client: { 
    type: mongoose.Schema.ObjectId, 
    ref: 'Client',
    required: true
  },
  assignedSalesRep: { type: mongoose.Schema.ObjectId, ref: 'Admin' },
  quote: { type: mongoose.Schema.ObjectId, ref: 'Quote' },
  serviceOrder: { type: mongoose.Schema.ObjectId, ref: 'ServiceOrder' },
  
  // Metadata
  createdBy: { type: mongoose.Schema.ObjectId, ref: 'Admin' },
  created: {
    type: Date,
    default: Date.now,
  },
  updated: {
    type: Date,
    default: Date.now,
  },
});

// Indexes for performance
schema.index({ client: 1 });
schema.index({ stage: 1 });
schema.index({ assignedSalesRep: 1 });
schema.index({ expectedCloseDate: 1 });
schema.index({ leadSource: 1 });
schema.index({ serviceType: 1 });

schema.plugin(require('mongoose-autopopulate'));

module.exports = mongoose.model('Opportunity', schema);
