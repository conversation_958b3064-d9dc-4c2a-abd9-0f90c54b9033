const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  removed: {
    type: Boolean,
    default: false,
  },
  enabled: {
    type: Boolean,
    default: true,
  },

  // Basic Information
  name: {
    type: String,
    required: true,
  },
  phone: String,
  country: String,
  address: String,
  email: String,

  // HVAC-Specific Fields
  buildingType: {
    type: String,
    enum: ['residential', 'commercial', 'industrial', 'office', 'retail', 'warehouse'],
    default: 'residential'
  },
  heatingSystem: String, // Current heating system
  coolingSystem: String, // Current cooling system
  serviceArea: String, // Geographic service area
  preferredTechnician: { type: mongoose.Schema.ObjectId, ref: 'Admin' },
  contractType: {
    type: String,
    enum: ['one_time', 'maintenance_contract', 'warranty', 'service_agreement'],
    default: 'one_time'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  hasPortalAccess: {
    type: Boolean,
    default: false
  },

  // Customer Intelligence
  profileCompleteness: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  healthScore: {
    type: Number,
    default: 50,
    min: 0,
    max: 100
  },
  churnProbability: {
    type: Number,
    default: 0,
    min: 0,
    max: 1
  },
  lifetimeValue: {
    type: Number,
    default: 0
  },
  aiInsights: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },

  createdBy: { type: mongoose.Schema.ObjectId, ref: 'Admin' },
  assigned: { type: mongoose.Schema.ObjectId, ref: 'Admin' },
  created: {
    type: Date,
    default: Date.now,
  },
  updated: {
    type: Date,
    default: Date.now,
  },
});

schema.plugin(require('mongoose-autopopulate'));

module.exports = mongoose.model('Client', schema);
