const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  removed: {
    type: Boolean,
    default: false,
  },
  enabled: {
    type: Boolean,
    default: true,
  },

  // Basic Equipment Information
  name: {
    type: String,
    required: true,
  },
  model: String,
  serialNumber: String,
  manufacturer: {
    type: String,
    enum: ['Daikin', 'LG', 'Mitsubishi', 'Carrier', 'Toshiba', 'Panasonic', 'Samsung', 'Fujitsu', 'Other'],
    required: true
  },
  
  // Equipment Type & Specifications
  type: {
    type: String,
    enum: ['air_conditioner', 'heat_pump', 'furnace', 'boiler', 'ventilation', 'ductwork', 'thermostat'],
    required: true
  },
  capacity: String, // BTU/kW capacity
  efficiency: String, // SEER/EER rating
  refrigerantType: {
    type: String,
    enum: ['R-32', 'R-410A', 'R-134a', 'R-22', 'R-407C', 'R-290'],
    default: 'R-32'
  },
  
  // Installation & Lifecycle
  installationDate: Date,
  warrantyExpiryDate: Date,
  lastMaintenanceDate: Date,
  nextMaintenanceDate: Date,
  
  // Health & Performance
  healthScore: {
    type: Number,
    default: 100,
    min: 0,
    max: 100
  },
  maintenancePredictions: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  failureProbability: {
    type: Number,
    default: 0,
    min: 0,
    max: 1
  },
  optimizationRecommendations: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  // Location & Assignment
  location: String, // Room/area where installed
  qrCode: String, // QR code for mobile scanning
  
  // Relationships
  client: { 
    type: mongoose.Schema.ObjectId, 
    ref: 'Client',
    required: true
  },
  assignedTechnician: { type: mongoose.Schema.ObjectId, ref: 'Admin' },
  
  // Metadata
  createdBy: { type: mongoose.Schema.ObjectId, ref: 'Admin' },
  created: {
    type: Date,
    default: Date.now,
  },
  updated: {
    type: Date,
    default: Date.now,
  },
});

// Indexes for performance
schema.index({ client: 1 });
schema.index({ type: 1 });
schema.index({ manufacturer: 1 });
schema.index({ nextMaintenanceDate: 1 });

schema.plugin(require('mongoose-autopopulate'));

module.exports = mongoose.model('Equipment', schema);
