# 🚀 FULMARK HVAC CRM - QUICK START GUIDE

## 📋 **WYMAGANIA SYSTEMOWE**

### **Backend Requirements**
- Node.js 18+ 
- MongoDB 5.0+
- npm lub yarn

### **Frontend Requirements**
- Node.js 18+
- React 18+
- Ant Design 5+

## ⚡ **SZYBKIE URUCHOMIENIE**

### **1. Backend Setup**
```bash
cd backend

# Install dependencies
npm install

# Add new dependency for drag & drop (if not already installed)
npm install react-beautiful-dnd

# Setup environment variables
cp .env.example .env
# Edit .env with your MongoDB connection string

# Start development server
npm run dev
```

### **2. Frontend Setup**
```bash
cd frontend

# Install dependencies
npm install

# Add react-beautiful-dnd if not already in package.json
npm install react-beautiful-dnd

# Start development server
npm start
```

### **3. Database Setup**
MongoDB będzie automatycznie tworz<PERSON><PERSON> kolek<PERSON>je dla nowych modeli:
- `equipments` - <PERSON><PERSON><PERSON><PERSON>t HVAC
- `serviceorders` - Zlecenia serwisowe
- `opportunities` - Możliwości sprzedaży

## 🎯 **NOWE FUNKCJE HVAC**

### **Equipment Management** (`/equipment`)
- ✅ Zarządzanie sprzętem HVAC
- ✅ Health Score tracking
- ✅ Maintenance scheduling
- ✅ QR code generation

### **Service Orders** (`/serviceorder`)
- ✅ Kanban Board z 8 etapami
- ✅ Drag & drop workflow
- ✅ 3 kategorie kalendarza
- ✅ Digital protocols

### **Sales Pipeline** (`/opportunity`)
- ✅ 7-stage sales process
- ✅ Lead scoring
- ✅ Pipeline value tracking
- ✅ HVAC-specific fields

### **Enhanced Dashboard** (`/`)
- ✅ HVAC KPIs
- ✅ Recent activities
- ✅ Maintenance alerts
- ✅ Sales metrics

## 🔧 **KONFIGURACJA HVAC**

### **1. Dodaj Pierwszego Klienta**
1. Idź do `/customer`
2. Kliknij "Add New Customer"
3. Wypełnij podstawowe dane + HVAC fields:
   - Building Type
   - Heating/Cooling System
   - Service Area
   - Contract Type

### **2. Dodaj Sprzęt HVAC**
1. Idź do `/equipment`
2. Kliknij "Add New Equipment"
3. Wypełnij dane sprzętu:
   - Manufacturer (Daikin, LG, Mitsubishi, etc.)
   - Type (air_conditioner, heat_pump, etc.)
   - Installation Date
   - Warranty Info

### **3. Utwórz Zlecenie Serwisowe**
1. Idź do `/serviceorder`
2. Wybierz tab "Kanban Board"
3. Kliknij "Add New Service Order"
4. Wypełnij dane zlecenia:
   - Client
   - Equipment
   - Type & Priority
   - Scheduled Date

### **4. Dodaj Możliwość Sprzedaży**
1. Idź do `/opportunity`
2. Wybierz tab "Sales Pipeline"
3. Kliknij "Add New Opportunity"
4. Wypełnij dane:
   - Client
   - Value & Probability
   - Service Type
   - Equipment Type

## 📊 **TESTOWANIE FUNKCJI**

### **Kanban Board Test**
1. Utwórz kilka Service Orders
2. Przeciągnij je między etapami
3. Sprawdź automatyczne aktualizacje dat
4. Przetestuj modal z detalami

### **Sales Pipeline Test**
1. Utwórz kilka Opportunities
2. Przeciągnij między etapami
3. Sprawdź aktualizację probability
4. Sprawdź kalkulację pipeline value

### **Dashboard Test**
1. Sprawdź HVAC KPIs
2. Sprawdź Recent Tables
3. Sprawdź responsywność
4. Sprawdź loading states

## 🐛 **TROUBLESHOOTING**

### **Backend Issues**
```bash
# Check MongoDB connection
npm run test:db

# Clear node_modules if needed
rm -rf node_modules
npm install

# Check logs
npm run dev --verbose
```

### **Frontend Issues**
```bash
# Clear cache
npm start -- --reset-cache

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Check for missing dependencies
npm audit
```

### **Common Issues**

**1. "react-beautiful-dnd not found"**
```bash
cd frontend
npm install react-beautiful-dnd
```

**2. "Equipment model not found"**
- Sprawdź czy backend jest uruchomiony
- Sprawdź connection string MongoDB
- Sprawdź czy nowe modele są w `/backend/src/models/appModels/`

**3. "Kanban board not loading"**
- Sprawdź Network tab w DevTools
- Sprawdź czy endpoint `/api/serviceorder/kanban` działa
- Sprawdź czy masz dane testowe

## 🎉 **SUKCES!**

Jeśli wszystko działa poprawnie, powinieneś zobaczyć:

✅ **Dashboard** z HVAC KPIs
✅ **Equipment** management page
✅ **Service Orders** z Kanban Board
✅ **Opportunities** z Sales Pipeline
✅ **Smooth drag & drop** functionality
✅ **Polish localization** throughout

## 📞 **WSPARCIE**

W przypadku problemów:
1. Sprawdź console logs (F12)
2. Sprawdź network requests
3. Sprawdź MongoDB connection
4. Sprawdź czy wszystkie dependencies są zainstalowane

**Gratulacje! Masz teraz w pełni funkcjonalny, zunifikowany HVAC CRM system!** 🎊
