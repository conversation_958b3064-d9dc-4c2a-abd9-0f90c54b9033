# WEAVIATE IMPLEMENTATION GUIDE
## Practical Code Examples for Hybrid Prisma + Weaviate Architecture

### 🚀 Quick Start Implementation

#### 1. Install Dependencies

```bash
# Core Weaviate client
pnpm add weaviate-ts-client

# Vector processing utilities
pnpm add @tensorflow/tfjs-node openai

# Environment variables
echo "WEAVIATE_HOST=localhost:8080" >> .env
echo "OPENAI_API_KEY=your_openai_key" >> .env
```

#### 2. Weaviate Client Setup

```typescript
// src/lib/weaviate.js
import weaviate from 'weaviate-ts-client';

const client = weaviate.client({
  scheme: process.env.WEAVIATE_SCHEME || 'http',
  host: process.env.WEAVIATE_HOST || 'localhost:8080',
  headers: {
    'X-OpenAI-Api-Key': process.env.OPENAI_API_KEY,
  },
});

// Test connection
export async function testConnection() {
  try {
    const result = await client.misc.metaGetter().do();
    console.log('✅ Weaviate connected:', result.hostname);
    return true;
  } catch (error) {
    console.error('❌ Weaviate connection failed:', error);
    return false;
  }
}

export default client;
```

#### 3. Schema Initialization

```typescript
// src/lib/weaviate/schema.js
import weaviate from '../weaviate.js';

export const HVAC_SCHEMA = {
  classes: [
    {
      class: 'HVACCustomer',
      description: 'HVAC customer profiles with semantic search capabilities',
      properties: [
        { name: 'customerId', dataType: ['string'], description: 'Prisma customer ID' },
        { name: 'fullName', dataType: ['string'], description: 'Customer full name' },
        { name: 'email', dataType: ['string'], description: 'Customer email address' },
        { name: 'company', dataType: ['string'], description: 'Company name' },
        { name: 'industry', dataType: ['string'], description: 'Industry sector' },
        { name: 'description', dataType: ['text'], description: 'Customer description and notes' },
        { name: 'location', dataType: ['geoCoordinates'], description: 'Customer location' },
        { name: 'tags', dataType: ['string[]'], description: 'Customer tags and categories' },
      ],
      vectorizer: 'text2vec-openai',
      moduleConfig: {
        'text2vec-openai': {
          model: 'ada',
          modelVersion: '002',
          type: 'text',
        },
      },
    },
    {
      class: 'HVACEquipment',
      description: 'HVAC equipment with specifications and maintenance history',
      properties: [
        { name: 'equipmentId', dataType: ['string'] },
        { name: 'model', dataType: ['string'] },
        { name: 'manufacturer', dataType: ['string'] },
        { name: 'category', dataType: ['string'] },
        { name: 'specifications', dataType: ['text'] },
        { name: 'maintenanceHistory', dataType: ['text'] },
        { name: 'installationNotes', dataType: ['text'] },
      ],
      vectorizer: 'text2vec-openai',
    },
    {
      class: 'ServiceTicket',
      description: 'Service tickets with issue descriptions and resolutions',
      properties: [
        { name: 'ticketId', dataType: ['string'] },
        { name: 'title', dataType: ['string'] },
        { name: 'description', dataType: ['text'] },
        { name: 'resolution', dataType: ['text'] },
        { name: 'category', dataType: ['string'] },
        { name: 'priority', dataType: ['string'] },
        { name: 'status', dataType: ['string'] },
      ],
      vectorizer: 'text2vec-openai',
    },
  ],
};

export async function initializeSchema() {
  try {
    // Check if schema exists
    const existingSchema = await weaviate.schema.getter().do();
    
    for (const classConfig of HVAC_SCHEMA.classes) {
      const classExists = existingSchema.classes?.some(c => c.class === classConfig.class);
      
      if (!classExists) {
        await weaviate.schema.classCreator().withClass(classConfig).do();
        console.log(`✅ Created Weaviate class: ${classConfig.class}`);
      } else {
        console.log(`ℹ️ Class already exists: ${classConfig.class}`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Schema initialization failed:', error);
    return false;
  }
}
```

#### 4. Unified Data Service

```typescript
// src/lib/services/unifiedDataService.js
import prisma from '../prisma.js';
import weaviate from '../weaviate.js';

export class UnifiedDataService {
  
  // Customer Management with Vector Sync
  async createCustomer(customerData) {
    try {
      // 1. Create in PostgreSQL via Prisma
      const customer = await prisma.contact.create({
        data: customerData,
        include: {
          owner: true,
          organization: true,
        },
      });

      // 2. Sync to Weaviate for semantic search
      await this.syncCustomerToWeaviate(customer);

      return customer;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  }

  async updateCustomer(id, updateData) {
    try {
      // 1. Update in PostgreSQL
      const customer = await prisma.contact.update({
        where: { id },
        data: updateData,
        include: {
          owner: true,
          organization: true,
        },
      });

      // 2. Update in Weaviate
      await this.syncCustomerToWeaviate(customer);

      return customer;
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  }

  async deleteCustomer(id) {
    try {
      // 1. Delete from Weaviate first
      await this.deleteCustomerFromWeaviate(id);

      // 2. Delete from PostgreSQL
      const customer = await prisma.contact.delete({
        where: { id },
      });

      return customer;
    } catch (error) {
      console.error('Error deleting customer:', error);
      throw error;
    }
  }

  // Semantic Search Capabilities
  async searchCustomersSemanticText(query, limit = 10) {
    try {
      const result = await weaviate
        .graphql
        .get()
        .withClassName('HVACCustomer')
        .withFields('customerId fullName email company description')
        .withNearText({
          concepts: [query],
          certainty: 0.7,
        })
        .withLimit(limit)
        .do();

      return result.data.Get.HVACCustomer || [];
    } catch (error) {
      console.error('Semantic search error:', error);
      return [];
    }
  }

  async findSimilarCustomers(customerId, limit = 5) {
    try {
      // Get customer vector
      const result = await weaviate
        .graphql
        .get()
        .withClassName('HVACCustomer')
        .withFields('customerId fullName email company')
        .withNearObject({
          id: customerId,
          certainty: 0.7,
        })
        .withLimit(limit + 1) // +1 to exclude self
        .do();

      const customers = result.data.Get.HVACCustomer || [];
      return customers.filter(c => c.customerId !== customerId);
    } catch (error) {
      console.error('Similar customers search error:', error);
      return [];
    }
  }

  // Hybrid Search (Traditional + Semantic)
  async searchCustomersHybrid(query, options = {}) {
    const { useSemanticSearch = true, limit = 20 } = options;

    try {
      let traditionalResults = [];
      let semanticResults = [];

      // Traditional Prisma search
      if (query) {
        traditionalResults = await prisma.contact.findMany({
          where: {
            OR: [
              { firstName: { contains: query, mode: 'insensitive' } },
              { lastName: { contains: query, mode: 'insensitive' } },
              { email: { contains: query, mode: 'insensitive' } },
              { company: { contains: query, mode: 'insensitive' } },
              { description: { contains: query, mode: 'insensitive' } },
            ],
          },
          include: {
            owner: true,
            organization: true,
          },
          take: Math.floor(limit / 2),
        });
      }

      // Semantic search via Weaviate
      if (useSemanticSearch && query) {
        const weaviateResults = await this.searchCustomersSemanticText(query, Math.floor(limit / 2));
        
        // Get full customer data from Prisma
        const customerIds = weaviateResults.map(r => r.customerId);
        if (customerIds.length > 0) {
          semanticResults = await prisma.contact.findMany({
            where: { id: { in: customerIds } },
            include: {
              owner: true,
              organization: true,
            },
          });
        }
      }

      // Combine and deduplicate results
      const combinedResults = this.deduplicateCustomers([...traditionalResults, ...semanticResults]);
      
      return {
        customers: combinedResults.slice(0, limit),
        traditionalCount: traditionalResults.length,
        semanticCount: semanticResults.length,
        totalCount: combinedResults.length,
      };
    } catch (error) {
      console.error('Hybrid search error:', error);
      throw error;
    }
  }

  // Weaviate Sync Operations
  async syncCustomerToWeaviate(customer) {
    try {
      const customerVector = {
        customerId: customer.id,
        fullName: `${customer.firstName} ${customer.lastName}`,
        email: customer.email || '',
        company: customer.company || '',
        description: this.buildCustomerDescription(customer),
        location: customer.latitude && customer.longitude ? {
          latitude: customer.latitude,
          longitude: customer.longitude,
        } : null,
        tags: this.extractCustomerTags(customer),
      };

      // Check if customer already exists in Weaviate
      const existing = await this.findCustomerInWeaviate(customer.id);
      
      if (existing) {
        // Update existing
        await weaviate
          .data
          .updater()
          .withId(existing.id)
          .withClassName('HVACCustomer')
          .withProperties(customerVector)
          .do();
      } else {
        // Create new
        await weaviate
          .data
          .creator()
          .withClassName('HVACCustomer')
          .withProperties(customerVector)
          .do();
      }

      console.log(`✅ Synced customer ${customer.id} to Weaviate`);
    } catch (error) {
      console.error(`❌ Failed to sync customer ${customer.id} to Weaviate:`, error);
    }
  }

  async deleteCustomerFromWeaviate(customerId) {
    try {
      const existing = await this.findCustomerInWeaviate(customerId);
      if (existing) {
        await weaviate.data.deleter().withId(existing.id).do();
        console.log(`✅ Deleted customer ${customerId} from Weaviate`);
      }
    } catch (error) {
      console.error(`❌ Failed to delete customer ${customerId} from Weaviate:`, error);
    }
  }

  async findCustomerInWeaviate(customerId) {
    try {
      const result = await weaviate
        .graphql
        .get()
        .withClassName('HVACCustomer')
        .withFields('_additional { id }')
        .withWhere({
          path: ['customerId'],
          operator: 'Equal',
          valueString: customerId,
        })
        .withLimit(1)
        .do();

      return result.data.Get.HVACCustomer?.[0] || null;
    } catch (error) {
      console.error('Error finding customer in Weaviate:', error);
      return null;
    }
  }

  // Utility Methods
  buildCustomerDescription(customer) {
    const parts = [
      customer.firstName,
      customer.lastName,
      customer.title,
      customer.department,
      customer.company,
      customer.description,
    ].filter(Boolean);

    return parts.join(' ');
  }

  extractCustomerTags(customer) {
    const tags = [];
    
    if (customer.title) tags.push(customer.title);
    if (customer.department) tags.push(customer.department);
    if (customer.organization?.industry) tags.push(customer.organization.industry);
    
    return tags;
  }

  deduplicateCustomers(customers) {
    const seen = new Set();
    return customers.filter(customer => {
      if (seen.has(customer.id)) {
        return false;
      }
      seen.add(customer.id);
      return true;
    });
  }
}

// Export singleton instance
export const unifiedDataService = new UnifiedDataService();
```

### 🔧 Integration with Existing Routes

```typescript
// src/routes/(app)/app/contacts/+page.server.js
import { unifiedDataService } from '$lib/services/unifiedDataService.js';

export async function load({ url, locals }) {
  try {
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const search = url.searchParams.get('search') || '';
    const useSemanticSearch = url.searchParams.get('semantic') === 'true';
    
    let result;
    
    if (search) {
      // Use hybrid search when there's a query
      result = await unifiedDataService.searchCustomersHybrid(search, {
        useSemanticSearch,
        limit,
      });
    } else {
      // Traditional pagination for browsing
      const skip = (page - 1) * limit;
      const customers = await prisma.contact.findMany({
        where: { organizationId: locals.user?.organizationId },
        include: { owner: true, organization: true },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      });
      
      const totalCount = await prisma.contact.count({
        where: { organizationId: locals.user?.organizationId },
      });
      
      result = {
        customers,
        totalCount,
        traditionalCount: customers.length,
        semanticCount: 0,
      };
    }

    return {
      ...result,
      currentPage: page,
      totalPages: Math.ceil(result.totalCount / limit),
      limit,
      search,
      useSemanticSearch,
    };
  } catch (err) {
    console.error('Error loading contacts:', err);
    throw error(500, 'Failed to load contacts');
  }
}
```

### 🎯 Next Steps

1. **Initialize Weaviate**: Run schema initialization
2. **Test Connection**: Verify Weaviate connectivity
3. **Migrate Existing Data**: Sync current customers to Weaviate
4. **Update UI**: Add semantic search toggle
5. **Monitor Performance**: Track query times and accuracy

This implementation provides a solid foundation for hybrid Prisma + Weaviate operations while maintaining backward compatibility with existing code.
