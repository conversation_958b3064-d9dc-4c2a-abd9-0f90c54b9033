# HVAC CRM Integration Plan
## Comprehensive Migration from hvac-remix to ButelkowyCRM/svelte-crm

### Executive Summary

This document outlines a comprehensive 12-week integration plan to migrate all HVAC CRM features from the mature hvac-remix system (98-100% complete) into the ButelkowyCRM/svelte-crm unified solution. The goal is to consolidate 55+ HVAC interface prototypes into a single, cohesive Svelte-based CRM system while maintaining all advanced functionality and Polish localization.

### Project Overview

**Source System**: hvac-remix
- React/Remix frontend with TypeScript
- 98-100% CRM completeness
- 20+ Prisma database models
- Production-ready with Docker deployment
- Comprehensive HVAC-specific features

**Target System**: ButelkowyCRM/svelte-crm
- Svelte 5 with TypeScript
- Basic CRM structure in place
- Modern build system with Vite
- <PERSON>portunity for performance optimization

### Strategic Rationale

1. **Consolidation Goal**: Unify 55 different HVAC interfaces into single solution
2. **Performance Benefits**: Svelte's compiled approach for better runtime performance
3. **Developer Experience**: Modern framework with simpler syntax and less boilerplate
4. **Future-Proofing**: Cutting-edge technology stack for long-term maintainability
5. **Bundle Optimization**: Smaller runtime footprint and faster loading times

## Phase 1: Foundation & Architecture (Weeks 1-2)

### 1.1 Project Setup & Environment Configuration

**Week 1 Tasks:**
- [ ] Enhanced Svelte project structure setup
- [ ] Development environment configuration
- [ ] CI/CD pipeline establishment
- [ ] Docker containerization setup
- [ ] Testing framework integration (Vitest, Playwright)

**Week 2 Tasks:**
- [ ] Database schema migration and enhancement
- [ ] API architecture design
- [ ] Component library foundation
- [ ] State management strategy implementation
- [ ] Build optimization configuration

### 1.2 Database Schema Migration

**Enhanced Prisma Schema Features:**
```prisma
// HVAC-Specific Models
model Customer {
  // Core fields from hvac-remix
  preferredTechnician String?
  contractType        String?
  priority           CustomerPriority
  hasPortalAccess    Boolean
  
  // Enhanced HVAC fields
  buildingType       BuildingType
  heatingSystem      String?
  coolingSystem      String?
  serviceArea        String?
}

model Equipment {
  // Lifecycle tracking
  installationDate     DateTime?
  warrantyExpiryDate   DateTime?
  lastMaintenanceDate  DateTime?
  nextMaintenanceDate  DateTime?
  healthScore         Int?
  
  // HVAC specifics
  refrigerantType     String?
  capacity           String?
  efficiency         String?
  qrCode             String?
}

model ServiceOrder {
  // 8-stage workflow
  stage              ServiceStage
  priority           ServicePriority
  type               ServiceType
  
  // Polish HVAC workflow
  // Nowe Zgłoszenie → Wycena → Zaplanowane → W Trakcie → 
  // Oczekiwanie na Części → Testowanie → Kontrola Jakości → 
  // Odbiory Techniczne → Zakończone → Gwarancja → Rozliczone → Archiwum
}
```

### 1.3 Component Architecture Design

**Atomic Design Implementation:**
- **Atoms**: Buttons, inputs, icons, labels
- **Molecules**: Form fields, cards, navigation items
- **Organisms**: Forms, tables, dashboards, kanban boards
- **Templates**: Page layouts, modal structures
- **Pages**: Complete route components

## Phase 2: Core CRM Features Migration (Weeks 3-6)

### 2.1 Customer Management System (Week 3)

**Migration Scope:**
- Customer profiles with HVAC-specific fields
- 360-degree customer view aggregation
- Customer map with geolocation
- Portal access management
- Customer lifecycle tracking

### 2.2 Sales Pipeline Implementation (Week 4)

**7-Stage Sales Pipeline:**
1. **Nowy Lead** (NEW_LEAD)
2. **Kwalifikowany** (QUALIFIED) 
3. **Propozycja** (PROPOSAL)
4. **Negocjacje** (NEGOTIATION)
5. **W Realizacji** (IN_PROGRESS)
6. **Zamknięty - Wygrany** (CLOSED_WON)
7. **Follow-up** (FOLLOW_UP)

### 2.3 Service Order Management (Week 5)

**8-Stage Service Workflow:**
1. **Backlog** - Nowe zgłoszenia
2. **Zaplanowane** - Scheduled
3. **W Trakcie** - In Progress  
4. **Oczekiwanie na Części** - Waiting for Parts
5. **Kontrola Jakości** - Quality Check
6. **Zakończone** - Completed
7. **Rozliczone** - Billed
8. **Archiwum** - Archived

### 2.4 Equipment Registry (Week 6)

**Equipment Lifecycle Management:**
- Installation and warranty tracking
- Maintenance scheduling with alerts
- QR code generation and scanning
- Health scoring algorithm
- Predictive maintenance recommendations

## Phase 3: Advanced Features Integration (Weeks 7-10)

### 3.1 Calendar System (Week 7)

**3-Category Calendar:**
- 🔧 **Serwis** (Service) - Maintenance and repairs
- 🏗️ **Nowa Instalacja** (New Installation) - Equipment installation
- 🔍 **Oględziny** (Inspection) - Site surveys and inspections

**Features:**
- Outlook integration
- Route optimization for technicians
- Automated scheduling based on priority
- Mobile calendar for field technicians

### 3.2 Financial Dashboard (Week 8)

**Financial Management Features:**
- Invoice generation and processing
- OCR document scanning
- Payment tracking and reminders
- Financial analytics and reporting
- Integration with Polish accounting standards

### 3.3 Document Management (Week 9)

**Document Processing:**
- Service report generation
- Digital signature integration
- PDF protocol creation
- Document OCR and indexing
- Automated document workflows

### 3.4 Polish Localization (Week 10)

**Complete Polish Translation:**
- UI components and labels
- HVAC-specific terminology
- Error messages and notifications
- Help documentation
- Email templates

## Phase 4: AI & Integration Features (Weeks 11-12)

### 4.1 GoSpine Backend Integration (Week 11)

**API Integration:**
- tRPC implementation for type safety
- Real-time data synchronization
- Error handling and retry logic
- Performance optimization
- Health monitoring

### 4.2 AI Features Integration (Week 12)

**AI Services:**
- **Bielik V3**: Polish language processing
- **Gemma3**: General AI analysis
- **Email Intelligence**: Automated email processing
- **Predictive Analytics**: Equipment failure prediction
- **Lead Scoring**: AI-powered opportunity ranking

## Technical Implementation Details

### State Management Strategy

```typescript
// stores/customer.ts
import { writable, derived } from 'svelte/store';
import type { Customer, Device, ServiceOrder } from '$lib/types';

export const customers = writable<Customer[]>([]);
export const selectedCustomer = writable<Customer | null>(null);

export const customerDevices = derived(
  [customers, selectedCustomer],
  ([$customers, $selectedCustomer]) => {
    if (!$selectedCustomer) return [];
    return $customers
      .find(c => c.id === $selectedCustomer.id)
      ?.devices || [];
  }
);

export const customerStore = {
  async getUnifiedProfile(customerId: string) {
    // Aggregate data from multiple sources
    const response = await fetch(`/api/customers/${customerId}/unified-profile`);
    return response.json();
  },

  async updateCustomer(customer: Customer) {
    // Update customer with optimistic updates
    customers.update(list =>
      list.map(c => c.id === customer.id ? customer : c)
    );

    const response = await fetch(`/api/customers/${customer.id}`, {
      method: 'PUT',
      body: JSON.stringify(customer)
    });

    if (!response.ok) {
      // Revert optimistic update on error
      // ... error handling
    }
  }
};
```

### Component Communication Patterns

```svelte
<!-- Parent Component -->
<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher<{
    customerUpdated: { customer: Customer };
    serviceOrderCreated: { serviceOrder: ServiceOrder };
  }>();

  function handleCustomerUpdate(customer: Customer) {
    dispatch('customerUpdated', { customer });
  }
</script>

<!-- Child Component -->
<CustomerForm
  {customer}
  on:customerUpdated={handleCustomerUpdate}
/>
```

### Svelte Component Examples

```svelte
<!-- CustomerProfile.svelte -->
<script lang="ts">
  import { customerStore } from '$lib/stores/customer';
  import { onMount } from 'svelte';

  export let customerId: string;

  let customer = $state();
  let devices = $state([]);
  let serviceHistory = $state([]);
  let communications = $state([]);

  onMount(async () => {
    // Load comprehensive customer data
    customer = await customerStore.getUnifiedProfile(customerId);
    devices = await customerStore.getDevices(customerId);
    serviceHistory = await customerStore.getServiceHistory(customerId);
    communications = await customerStore.getCommunications(customerId);
  });
</script>

<div class="customer-profile">
  <CustomerHeader {customer} />
  <CustomerTabs>
    <TabPanel title="Przegląd">
      <CustomerOverview {customer} {devices} />
    </TabPanel>
    <TabPanel title="Urządzenia">
      <EquipmentList {devices} />
    </TabPanel>
    <TabPanel title="Historia Serwisu">
      <ServiceHistory history={serviceHistory} />
    </TabPanel>
    <TabPanel title="Komunikacja">
      <CommunicationTimeline {communications} />
    </TabPanel>
  </CustomerTabs>
</div>
```

```svelte
<!-- SalesPipeline.svelte -->
<script lang="ts">
  import { dndzone } from 'svelte-dnd-action';
  import { pipelineStore } from '$lib/stores/pipeline';

  let stages = $state([
    { id: 'NEW_LEAD', name: 'Nowy Lead', opportunities: [] },
    { id: 'QUALIFIED', name: 'Kwalifikowany', opportunities: [] },
    { id: 'PROPOSAL', name: 'Propozycja', opportunities: [] },
    { id: 'NEGOTIATION', name: 'Negocjacje', opportunities: [] },
    { id: 'IN_PROGRESS', name: 'W Realizacji', opportunities: [] },
    { id: 'CLOSED_WON', name: 'Zamknięty - Wygrany', opportunities: [] },
    { id: 'FOLLOW_UP', name: 'Follow-up', opportunities: [] }
  ]);

  function handleDrop(stageId: string, event: CustomEvent) {
    const { items } = event.detail;
    pipelineStore.moveOpportunity(items, stageId);
  }
</script>

<div class="pipeline-board">
  {#each stages as stage}
    <div class="pipeline-stage">
      <h3>{stage.name}</h3>
      <div
        use:dndzone={{ items: stage.opportunities }}
        on:consider={e => handleDrop(stage.id, e)}
        on:finalize={e => handleDrop(stage.id, e)}
      >
        {#each stage.opportunities as opportunity}
          <OpportunityCard {opportunity} />
        {/each}
      </div>
    </div>
  {/each}
</div>

<style>
  .pipeline-board {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    padding: 1rem;
  }

  .pipeline-stage {
    min-width: 300px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
  }
</style>
```

## Migration Strategy

### 1. Incremental Migration Approach

**Phase-by-Phase Cutover:**
- Week 1-2: Development environment setup
- Week 3-4: Customer management migration
- Week 5-6: Sales pipeline migration  
- Week 7-8: Service management migration
- Week 9-10: Calendar and financial migration
- Week 11-12: AI and final integration

### 2. Data Migration Plan

**Database Migration Steps:**
1. **Schema Analysis**: Compare hvac-remix and svelte-crm schemas
2. **Data Export**: Extract all data from hvac-remix PostgreSQL
3. **Data Transformation**: Convert to new schema format
4. **Data Validation**: Ensure integrity and completeness
5. **Data Import**: Load into new Svelte-CRM database
6. **Verification**: Validate all data migrated correctly

**Migration Script Example:**
```typescript
// scripts/migrate-data.ts
import { PrismaClient as OldPrisma } from '../hvac-remix/prisma/generated/client';
import { PrismaClient as NewPrisma } from './prisma/generated/client';

const oldDb = new OldPrisma();
const newDb = new NewPrisma();

async function migrateCustomers() {
  const oldCustomers = await oldDb.customer.findMany({
    include: {
      devices: true,
      serviceOrders: true,
      invoices: true
    }
  });

  for (const customer of oldCustomers) {
    await newDb.customer.create({
      data: {
        // Map old schema to new schema
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        city: customer.city,
        postalCode: customer.postalCode,
        // HVAC-specific fields
        preferredTechnician: customer.preferredTechnician,
        contractType: customer.contractType,
        priority: customer.priority,
        // Create related records
        devices: {
          create: customer.devices.map(device => ({
            name: device.name,
            model: device.model,
            serialNumber: device.serialNumber,
            manufacturer: device.manufacturer,
            installationDate: device.installationDate,
            warrantyExpiryDate: device.warrantyExpiryDate,
            // HVAC device fields
            type: device.type,
            capacity: device.capacity,
            efficiency: device.efficiency,
            refrigerantType: device.refrigerantType,
            lastMaintenanceDate: device.lastMaintenanceDate,
            nextMaintenanceDate: device.nextMaintenanceDate
          }))
        },
        serviceOrders: {
          create: customer.serviceOrders.map(order => ({
            title: order.title,
            description: order.description,
            status: order.status,
            priority: order.priority,
            type: order.type,
            scheduledDate: order.scheduledDate,
            completedDate: order.completedDate,
            estimatedCost: order.estimatedCost,
            actualCost: order.actualCost
          }))
        }
      }
    });
  }
}

async function migrateOpportunities() {
  const oldOpportunities = await oldDb.opportunity.findMany();

  for (const opportunity of oldOpportunities) {
    await newDb.opportunity.create({
      data: {
        name: opportunity.name,
        description: opportunity.description,
        stage: opportunity.stage,
        probability: opportunity.probability,
        value: opportunity.value,
        currency: opportunity.currency,
        expectedCloseDate: opportunity.expectedCloseDate,
        actualCloseDate: opportunity.actualCloseDate,
        // HVAC-specific fields
        serviceType: opportunity.serviceType,
        equipmentType: opportunity.equipmentType,
        installationComplexity: opportunity.installationComplexity,
        roomCount: opportunity.roomCount,
        totalArea: opportunity.totalArea,
        buildingType: opportunity.buildingType,
        leadSource: opportunity.leadSource,
        customerId: opportunity.customerId
      }
    });
  }
}

async function migrateCalendarEntries() {
  const oldEntries = await oldDb.calendarEntry.findMany();

  for (const entry of oldEntries) {
    await newDb.calendarEntry.create({
      data: {
        title: entry.title,
        description: entry.description,
        startTime: entry.startTime,
        endTime: entry.endTime,
        location: entry.location,
        isAllDay: entry.isAllDay,
        color: entry.color,
        category: entry.category,
        // HVAC-specific fields
        customer: entry.customer,
        technician: entry.technician,
        device: entry.device,
        deviceCount: entry.deviceCount,
        priority: entry.priority,
        status: entry.status,
        clientContact: entry.clientContact,
        technicalIssues: entry.technicalIssues,
        spareParts: entry.spareParts,
        costAmount: entry.costAmount,
        costCurrency: entry.costCurrency,
        costDescription: entry.costDescription,
        // AI analysis fields
        keywords: entry.keywords,
        semanticAnalysis: entry.semanticAnalysis,
        analysisTimestamp: entry.analysisTimestamp,
        userId: entry.userId,
        serviceOrderId: entry.serviceOrderId
      }
    });
  }
}

// Main migration function
async function runMigration() {
  try {
    console.log('Starting data migration...');

    await migrateCustomers();
    console.log('✅ Customers migrated');

    await migrateOpportunities();
    console.log('✅ Opportunities migrated');

    await migrateCalendarEntries();
    console.log('✅ Calendar entries migrated');

    console.log('🎉 Migration completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await oldDb.$disconnect();
    await newDb.$disconnect();
  }
}

// Run migration
runMigration().catch(console.error);
```

### 3. Testing Strategy

**Testing Pyramid:**
- **Unit Tests**: Individual component testing with Vitest
- **Integration Tests**: API endpoint testing
- **E2E Tests**: Complete workflow testing with Playwright
- **Performance Tests**: Load and stress testing
- **User Acceptance Tests**: Business workflow validation

**Test Examples:**
```typescript
// tests/components/CustomerProfile.test.ts
import { render, screen } from '@testing-library/svelte';
import { vi } from 'vitest';
import CustomerProfile from '$lib/components/CustomerProfile.svelte';

// Mock the customer store
vi.mock('$lib/stores/customer', () => ({
  customerStore: {
    getUnifiedProfile: vi.fn().mockResolvedValue({
      id: '1',
      name: 'Jan Kowalski',
      email: '<EMAIL>',
      phone: '+**************',
      address: 'ul. Testowa 1, Warszawa'
    }),
    getDevices: vi.fn().mockResolvedValue([]),
    getServiceHistory: vi.fn().mockResolvedValue([]),
    getCommunications: vi.fn().mockResolvedValue([])
  }
}));

test('displays customer information correctly', async () => {
  const customer = {
    id: '1',
    name: 'Jan Kowalski',
    email: '<EMAIL>',
    phone: '+**************',
    address: 'ul. Testowa 1, Warszawa'
  };

  render(CustomerProfile, { props: { customerId: '1' } });

  // Wait for async data loading
  await screen.findByText('Jan Kowalski');

  expect(screen.getByText('Jan Kowalski')).toBeInTheDocument();
  expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  expect(screen.getByText('+**************')).toBeInTheDocument();
});

test('handles customer update correctly', async () => {
  const mockUpdate = vi.fn();
  vi.mocked(customerStore.updateCustomer).mockImplementation(mockUpdate);

  render(CustomerProfile, { props: { customerId: '1' } });

  // Simulate customer update
  const updateButton = screen.getByRole('button', { name: /aktualizuj/i });
  await fireEvent.click(updateButton);

  expect(mockUpdate).toHaveBeenCalledWith(expect.objectContaining({
    id: '1',
    name: 'Jan Kowalski'
  }));
});
```

```typescript
// tests/api/customers.test.ts
import { expect, test } from '@playwright/test';

test('customer CRUD operations', async ({ page }) => {
  await page.goto('/app/customers');

  // Create customer
  await page.click('[data-testid="add-customer"]');
  await page.fill('[name="name"]', 'Test Customer');
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="phone"]', '+**************');
  await page.fill('[name="address"]', 'ul. Testowa 1');
  await page.fill('[name="city"]', 'Warszawa');
  await page.click('[type="submit"]');

  // Verify customer created
  await expect(page.locator('text=Test Customer')).toBeVisible();

  // Edit customer
  await page.click('[data-testid="edit-customer"]');
  await page.fill('[name="name"]', 'Updated Customer');
  await page.click('[type="submit"]');

  // Verify customer updated
  await expect(page.locator('text=Updated Customer')).toBeVisible();

  // Delete customer
  await page.click('[data-testid="delete-customer"]');
  await page.click('[data-testid="confirm-delete"]');

  // Verify customer deleted
  await expect(page.locator('text=Updated Customer')).not.toBeVisible();
});

test('sales pipeline drag and drop', async ({ page }) => {
  await page.goto('/app/opportunities/pipeline');

  // Create opportunity
  await page.click('[data-testid="add-opportunity"]');
  await page.fill('[name="name"]', 'Test Opportunity');
  await page.fill('[name="value"]', '10000');
  await page.click('[type="submit"]');

  // Verify opportunity in NEW_LEAD stage
  const newLeadStage = page.locator('[data-stage="NEW_LEAD"]');
  await expect(newLeadStage.locator('text=Test Opportunity')).toBeVisible();

  // Drag opportunity to QUALIFIED stage
  const opportunity = newLeadStage.locator('[data-testid="opportunity-card"]');
  const qualifiedStage = page.locator('[data-stage="QUALIFIED"]');

  await opportunity.dragTo(qualifiedStage);

  // Verify opportunity moved
  await expect(qualifiedStage.locator('text=Test Opportunity')).toBeVisible();
  await expect(newLeadStage.locator('text=Test Opportunity')).not.toBeVisible();
});

test('calendar integration', async ({ page }) => {
  await page.goto('/app/calendar');

  // Create calendar entry
  await page.click('[data-testid="add-event"]');
  await page.fill('[name="title"]', 'Serwis klimatyzacji');
  await page.selectOption('[name="category"]', 'SERVICE');
  await page.fill('[name="customer"]', 'Jan Kowalski');
  await page.fill('[name="technician"]', 'Tomasz Nowak');
  await page.click('[type="submit"]');

  // Verify event created
  await expect(page.locator('text=Serwis klimatyzacji')).toBeVisible();

  // Verify category icon
  await expect(page.locator('[data-category="SERVICE"] .icon-wrench')).toBeVisible();
});
```

## Resource Requirements

### Development Team (12 weeks)

**Core Team:**
- **2-3 Senior Svelte Developers**: Component development, state management
- **1 Backend Integration Specialist**: API integration, database migration
- **1 UI/UX Designer**: Polish localization, design system
- **1 DevOps Engineer**: Infrastructure, deployment, monitoring
- **1 QA Engineer**: Testing, quality assurance

**Estimated Effort**: 360-480 developer hours
**Total Estimated Cost**: $74,400-99,000

## Deployment Strategy

### 1. Infrastructure Setup

**Docker Configuration:**
```dockerfile
# Dockerfile
FROM node:20-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM node:20-alpine AS runner
WORKDIR /app

COPY --from=builder /app/build ./build
COPY --from=builder /app/package*.json ./
RUN npm ci --production

EXPOSE 3000
CMD ["node", "build"]
```

**Docker Compose:**
```yaml
# docker-compose.yml
version: '3.8'
services:
  svelte-crm:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=************************************/svelte_crm
      - GOSPINE_API_URL=http://gospine:8080
      - REDIS_URL=redis://redis:6379
      - BIELIK_API_URL=http://bielik:8877
      - GEMMA_API_URL=http://gemma:8878
    depends_on:
      - postgres
      - redis
      - gospine

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: svelte_crm
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  gospine:
    image: gospine:latest
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=************************************/gospine
    depends_on:
      - postgres

  bielik:
    image: bielik-v3:latest
    ports:
      - "8877:8877"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  gemma:
    image: gemma3-4b:latest
    ports:
      - "8878:8878"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

volumes:
  postgres_data:
```

### 2. CI/CD Pipeline

**GitHub Actions Workflow:**
```yaml
# .github/workflows/deploy.yml
name: Deploy Svelte CRM

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run type checking
        run: npm run check

      - name: Run unit tests
        run: npm run test

      - name: Run E2E tests
        run: npm run test:e2e

  build:
    needs: test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Deploy to production
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /opt/svelte-crm
            docker-compose pull
            docker-compose up -d
            docker system prune -f
```

### 3. Environment Configuration

**Production Environment Variables:**
```bash
# .env.production
DATABASE_URL=***********************************************/svelte_crm_prod
REDIS_URL=redis://redis:6379
GOSPINE_API_URL=http://gospine:8080
BIELIK_API_URL=http://bielik:8877
GEMMA_API_URL=http://gemma:8878

# Security
JWT_SECRET=your-super-secure-jwt-secret
ENCRYPTION_KEY=your-32-character-encryption-key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Storage
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=hvac-crm-files

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
LOG_LEVEL=info

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_EMAIL_INTELLIGENCE=true
ENABLE_PREDICTIVE_MAINTENANCE=true
```

## Risk Management

### 1. Technical Risks

**Risk**: Data loss during migration
**Mitigation**:
- Complete database backup before migration
- Parallel running systems during transition
- Rollback procedures in place
- Data validation at each step
- Incremental migration with checkpoints

**Risk**: Performance degradation
**Mitigation**:
- Performance benchmarking before migration
- Load testing with production data volumes
- Optimization of critical paths
- Monitoring and alerting setup
- Caching strategies implementation

**Risk**: Integration failures with GoSpine/AI services
**Mitigation**:
- Comprehensive API testing
- Gradual integration rollout
- Fallback mechanisms for AI services
- Health checks and monitoring
- Circuit breaker patterns

**Risk**: Svelte learning curve for team
**Mitigation**:
- Team training on Svelte 5 features
- Code review processes
- Pair programming sessions
- Documentation and best practices
- Gradual complexity increase

### 2. Business Risks

**Risk**: User adoption resistance
**Mitigation**:
- User training programs
- Gradual feature rollout
- Feedback collection and iteration
- Support documentation in Polish
- Change management process

**Risk**: Business continuity disruption
**Mitigation**:
- Blue-green deployment strategy
- Minimal downtime windows (< 2 hours)
- Emergency rollback procedures
- 24/7 support during transition
- Communication plan for users

**Risk**: Feature regression
**Mitigation**:
- Comprehensive test coverage
- User acceptance testing
- Feature parity validation
- Regression testing suite
- Staged rollout approach

### 3. Project Risks

**Risk**: Timeline delays
**Mitigation**:
- Buffer time in schedule (20%)
- Regular milestone reviews
- Risk assessment at each phase
- Resource flexibility
- Scope prioritization

**Risk**: Resource availability
**Mitigation**:
- Cross-training team members
- Documentation of all processes
- Backup resource identification
- Knowledge sharing sessions
- External consultant availability

## Timeline Summary

| Phase | Duration | Key Deliverables | Success Criteria |
|-------|----------|------------------|------------------|
| **Phase 1** | Weeks 1-2 | Foundation & Architecture | Development environment ready, schema migrated |
| **Phase 2** | Weeks 3-6 | Core CRM Features | Customer management, sales pipeline, service orders |
| **Phase 3** | Weeks 7-10 | Advanced Features | Calendar, financial, documents, localization |
| **Phase 4** | Weeks 11-12 | AI & Integration | GoSpine integration, AI features, final testing |

## Success Metrics

### Technical Metrics
- **Performance**: Page load times < 2 seconds
- **Bundle Size**: < 500KB initial bundle
- **Test Coverage**: > 90% code coverage
- **Uptime**: 99.9% availability
- **API Response**: < 200ms average response time

### Business Metrics
- **Feature Parity**: 100% of hvac-remix features migrated
- **Data Integrity**: Zero data loss during migration
- **User Satisfaction**: > 4.5/5 user rating
- **Adoption Rate**: > 95% user adoption within 30 days

## Conclusion

This comprehensive integration plan provides a structured approach to migrating the mature hvac-remix system to the modern Svelte-based ButelkowyCRM platform. The 12-week timeline balances thorough development with business continuity needs.

**Key Success Factors:**
1. **Incremental Migration**: Reduces risk through gradual transition
2. **Comprehensive Testing**: Ensures quality and reliability
3. **User-Centric Approach**: Maintains focus on business value
4. **Risk Mitigation**: Proactive identification and management of risks
5. **Performance Focus**: Leverages Svelte's performance benefits

**Next Steps:**
1. Stakeholder approval of integration plan
2. Team assembly and resource allocation
3. Development environment setup
4. Phase 1 kickoff and execution

The successful completion of this integration will result in a unified, high-performance HVAC CRM system that consolidates all existing functionality while providing a modern, maintainable foundation for future enhancements.
